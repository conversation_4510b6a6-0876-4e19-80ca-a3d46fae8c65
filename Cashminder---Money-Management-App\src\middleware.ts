import { NextRequest, NextResponse } from 'next/server';
import { verifyToken, extractTokenFromHeader } from './lib/jwt';

export async function middleware(request: NextRequest) {
  // Temporarily disabled middleware for debugging
  return NextResponse.next();
}

// Configure which routes use this middleware
export const config = {
  matcher: [
    '/api/:path*',
    // Exclude auth-related API routes
    '/((?!api/auth|_next/static|_next/image|favicon.ico).*)',
  ],
};
