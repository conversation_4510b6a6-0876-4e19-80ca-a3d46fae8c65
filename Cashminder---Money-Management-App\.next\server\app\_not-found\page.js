/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/_not-found/page";
exports.ids = ["app/_not-found/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5CUsers%5Cshiva%5COneDrive%5CDesktop%5Cmk%5CCashminder---Money-Management-App%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cshiva%5COneDrive%5CDesktop%5Cmk%5CCashminder---Money-Management-App&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5CUsers%5Cshiva%5COneDrive%5CDesktop%5Cmk%5CCashminder---Money-Management-App%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cshiva%5COneDrive%5CDesktop%5Cmk%5CCashminder---Money-Management-App&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst notFound0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n          children: [\"/_not-found\", {\n            children: ['__PAGE__', {}, {\n              page: [\n                notFound0,\n                \"next/dist/client/components/not-found-error\"\n              ]\n            }]\n          }, {}]\n        },\n        {\n        'layout': [module1, \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [module2, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module3, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module4, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/_not-found/page\",\n        pathname: \"/_not-found\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5CUsers%5Cshiva%5COneDrive%5CDesktop%5Cmk%5CCashminder---Money-Management-App%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cshiva%5COneDrive%5CDesktop%5Cmk%5CCashminder---Money-Management-App&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Orbitron%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-orbitron%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22orbitron%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Rajdhani%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-rajdhani%5C%22%2C%5C%22weight%5C%22%3A%5B%5C%22300%5C%22%2C%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22600%5C%22%2C%5C%22700%5C%22%5D%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22rajdhani%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Audiowide%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-audiowide%5C%22%2C%5C%22weight%5C%22%3A%5B%5C%22400%5C%22%5D%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22audiowide%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5CFuturisticNavbar.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Csrc%5C%5Ccomponents%5C%5CThemeWrapper.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Orbitron%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-orbitron%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22orbitron%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Rajdhani%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-rajdhani%5C%22%2C%5C%22weight%5C%22%3A%5B%5C%22300%5C%22%2C%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22600%5C%22%2C%5C%22700%5C%22%5D%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22rajdhani%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Audiowide%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-audiowide%5C%22%2C%5C%22weight%5C%22%3A%5B%5C%22400%5C%22%5D%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22audiowide%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5CFuturisticNavbar.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Csrc%5C%5Ccomponents%5C%5CThemeWrapper.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/layout/FuturisticNavbar.tsx */ \"(rsc)/./src/components/layout/FuturisticNavbar.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ThemeWrapper.tsx */ \"(rsc)/./src/components/ThemeWrapper.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Orbitron%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-orbitron%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22orbitron%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Rajdhani%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-rajdhani%5C%22%2C%5C%22weight%5C%22%3A%5B%5C%22300%5C%22%2C%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22600%5C%22%2C%5C%22700%5C%22%5D%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22rajdhani%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Audiowide%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-audiowide%5C%22%2C%5C%22weight%5C%22%3A%5B%5C%22400%5C%22%5D%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22audiowide%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5CFuturisticNavbar.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Csrc%5C%5Ccomponents%5C%5CThemeWrapper.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"94bb89cc64ba\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHNoaXZhXFxPbmVEcml2ZVxcRGVza3RvcFxcbWtcXENhc2htaW5kZXItLS1Nb25leS1NYW5hZ2VtZW50LUFwcFxcc3JjXFxhcHBcXGdsb2JhbHMuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiOTRiYjg5Y2M2NGJhXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_variable_font_inter_subsets_latin_display_swap_variableName_inter___WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"variable\":\"--font-inter\",\"subsets\":[\"latin\"],\"display\":\"swap\"}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-inter\\\",\\\"subsets\\\":[\\\"latin\\\"],\\\"display\\\":\\\"swap\\\"}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_variable_font_inter_subsets_latin_display_swap_variableName_inter___WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_variable_font_inter_subsets_latin_display_swap_variableName_inter___WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist\",\"arguments\":[{\"variable\":\"--font-geist-sans\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistSans\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Geist\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-sans\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistSans\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist_Mono\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistMono\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Geist_Mono\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-mono\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistMono\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Orbitron_arguments_variable_font_orbitron_subsets_latin_display_swap_variableName_orbitron___WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Orbitron\",\"arguments\":[{\"variable\":\"--font-orbitron\",\"subsets\":[\"latin\"],\"display\":\"swap\"}],\"variableName\":\"orbitron\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Orbitron\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-orbitron\\\",\\\"subsets\\\":[\\\"latin\\\"],\\\"display\\\":\\\"swap\\\"}],\\\"variableName\\\":\\\"orbitron\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Orbitron_arguments_variable_font_orbitron_subsets_latin_display_swap_variableName_orbitron___WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Orbitron_arguments_variable_font_orbitron_subsets_latin_display_swap_variableName_orbitron___WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Rajdhani_arguments_variable_font_rajdhani_weight_300_400_500_600_700_subsets_latin_display_swap_variableName_rajdhani___WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Rajdhani\",\"arguments\":[{\"variable\":\"--font-rajdhani\",\"weight\":[\"300\",\"400\",\"500\",\"600\",\"700\"],\"subsets\":[\"latin\"],\"display\":\"swap\"}],\"variableName\":\"rajdhani\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Rajdhani\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-rajdhani\\\",\\\"weight\\\":[\\\"300\\\",\\\"400\\\",\\\"500\\\",\\\"600\\\",\\\"700\\\"],\\\"subsets\\\":[\\\"latin\\\"],\\\"display\\\":\\\"swap\\\"}],\\\"variableName\\\":\\\"rajdhani\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Rajdhani_arguments_variable_font_rajdhani_weight_300_400_500_600_700_subsets_latin_display_swap_variableName_rajdhani___WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Rajdhani_arguments_variable_font_rajdhani_weight_300_400_500_600_700_subsets_latin_display_swap_variableName_rajdhani___WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Audiowide_arguments_variable_font_audiowide_weight_400_subsets_latin_display_swap_variableName_audiowide___WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Audiowide\",\"arguments\":[{\"variable\":\"--font-audiowide\",\"weight\":[\"400\"],\"subsets\":[\"latin\"],\"display\":\"swap\"}],\"variableName\":\"audiowide\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Audiowide\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-audiowide\\\",\\\"weight\\\":[\\\"400\\\"],\\\"subsets\\\":[\\\"latin\\\"],\\\"display\\\":\\\"swap\\\"}],\\\"variableName\\\":\\\"audiowide\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Audiowide_arguments_variable_font_audiowide_weight_400_subsets_latin_display_swap_variableName_audiowide___WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Audiowide_arguments_variable_font_audiowide_weight_400_subsets_latin_display_swap_variableName_audiowide___WEBPACK_IMPORTED_MODULE_9__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _components_layout_FuturisticNavbar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/layout/FuturisticNavbar */ \"(rsc)/./src/components/layout/FuturisticNavbar.tsx\");\n/* harmony import */ var _components_ThemeWrapper__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ThemeWrapper */ \"(rsc)/./src/components/ThemeWrapper.tsx\");\n\n\n\n\n\n\n\n\n\n\nconst metadata = {\n    title: \"Cashminder - Personal Finance Manager\",\n    description: \"Track your expenses, manage your budget, and achieve your financial goals\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"script\", {\n                    dangerouslySetInnerHTML: {\n                        __html: `\n            // Block Sentry requests to prevent console errors\n            const originalFetch = window.fetch;\n            window.fetch = function(url, options) {\n              if (url && typeof url === 'string' && url.includes('sentry')) {\n                console.log('Blocked Sentry request:', url);\n                return Promise.resolve(new Response('', { status: 200 }));\n              }\n              return originalFetch.apply(this, arguments);\n            };\n          `\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 62,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 57,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_4___default().variable)} ${(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_5___default().variable)} ${(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_variable_font_inter_subsets_latin_display_swap_variableName_inter___WEBPACK_IMPORTED_MODULE_6___default().variable)} ${(next_font_google_target_css_path_src_app_layout_tsx_import_Orbitron_arguments_variable_font_orbitron_subsets_latin_display_swap_variableName_orbitron___WEBPACK_IMPORTED_MODULE_7___default().variable)} ${(next_font_google_target_css_path_src_app_layout_tsx_import_Rajdhani_arguments_variable_font_rajdhani_weight_300_400_500_600_700_subsets_latin_display_swap_variableName_rajdhani___WEBPACK_IMPORTED_MODULE_8___default().variable)} ${(next_font_google_target_css_path_src_app_layout_tsx_import_Audiowide_arguments_variable_font_audiowide_weight_400_subsets_latin_display_swap_variableName_audiowide___WEBPACK_IMPORTED_MODULE_9___default().variable)} font-sans antialiased bg-gray-50 dark:bg-gray-900 min-h-screen transition-colors duration-300`,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ThemeWrapper__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col min-h-screen\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-grow\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_FuturisticNavbar__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\layout.tsx\",\n                                    lineNumber: 83,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                                    className: \"py-10\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"px-4 mx-auto max-w-7xl sm:px-6 lg:px-8\",\n                                        children: children\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\layout.tsx\",\n                                        lineNumber: 85,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\layout.tsx\",\n                                    lineNumber: 84,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\layout.tsx\",\n                            lineNumber: 81,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 80,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 79,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 76,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 56,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/ThemeWrapper.tsx":
/*!*****************************************!*\
  !*** ./src/components/ThemeWrapper.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\components\\\\ThemeWrapper.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\OneDrive\\Desktop\\mk\\Cashminder---Money-Management-App\\src\\components\\ThemeWrapper.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/components/layout/FuturisticNavbar.tsx":
/*!****************************************************!*\
  !*** ./src/components/layout/FuturisticNavbar.tsx ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\components\\\\layout\\\\FuturisticNavbar.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\OneDrive\\Desktop\\mk\\Cashminder---Money-Management-App\\src\\components\\layout\\FuturisticNavbar.tsx",
"default",
));


/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Orbitron%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-orbitron%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22orbitron%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Rajdhani%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-rajdhani%5C%22%2C%5C%22weight%5C%22%3A%5B%5C%22300%5C%22%2C%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22600%5C%22%2C%5C%22700%5C%22%5D%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22rajdhani%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Audiowide%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-audiowide%5C%22%2C%5C%22weight%5C%22%3A%5B%5C%22400%5C%22%5D%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22audiowide%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5CFuturisticNavbar.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Csrc%5C%5Ccomponents%5C%5CThemeWrapper.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Orbitron%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-orbitron%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22orbitron%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Rajdhani%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-rajdhani%5C%22%2C%5C%22weight%5C%22%3A%5B%5C%22300%5C%22%2C%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22600%5C%22%2C%5C%22700%5C%22%5D%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22rajdhani%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Audiowide%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-audiowide%5C%22%2C%5C%22weight%5C%22%3A%5B%5C%22400%5C%22%5D%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22audiowide%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5CFuturisticNavbar.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Csrc%5C%5Ccomponents%5C%5CThemeWrapper.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/layout/FuturisticNavbar.tsx */ \"(ssr)/./src/components/layout/FuturisticNavbar.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ThemeWrapper.tsx */ \"(ssr)/./src/components/ThemeWrapper.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Orbitron%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-orbitron%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22orbitron%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Rajdhani%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-rajdhani%5C%22%2C%5C%22weight%5C%22%3A%5B%5C%22300%5C%22%2C%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22600%5C%22%2C%5C%22700%5C%22%5D%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22rajdhani%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Audiowide%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-audiowide%5C%22%2C%5C%22weight%5C%22%3A%5B%5C%22400%5C%22%5D%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22audiowide%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5CFuturisticNavbar.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Csrc%5C%5Ccomponents%5C%5CThemeWrapper.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/components/ThemeWrapper.tsx":
/*!*****************************************!*\
  !*** ./src/components/ThemeWrapper.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ThemeWrapper)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _context_ThemeContext__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/context/ThemeContext */ \"(ssr)/./src/context/ThemeContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction ThemeWrapper({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_context_ThemeContext__WEBPACK_IMPORTED_MODULE_1__.ThemeProvider, {\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\components\\\\ThemeWrapper.tsx\",\n        lineNumber: 7,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9UaGVtZVdyYXBwZXIudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBRXVEO0FBRXhDLFNBQVNDLGFBQWEsRUFBRUMsUUFBUSxFQUFpQztJQUM5RSxxQkFDRSw4REFBQ0YsZ0VBQWFBO2tCQUNYRTs7Ozs7O0FBR1AiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcc2hpdmFcXE9uZURyaXZlXFxEZXNrdG9wXFxta1xcQ2FzaG1pbmRlci0tLU1vbmV5LU1hbmFnZW1lbnQtQXBwXFxzcmNcXGNvbXBvbmVudHNcXFRoZW1lV3JhcHBlci50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xuXG5pbXBvcnQgeyBUaGVtZVByb3ZpZGVyIH0gZnJvbSAnQC9jb250ZXh0L1RoZW1lQ29udGV4dCc7XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFRoZW1lV3JhcHBlcih7IGNoaWxkcmVuIH06IHsgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZSB9KSB7XG4gIHJldHVybiAoXG4gICAgPFRoZW1lUHJvdmlkZXI+XG4gICAgICB7Y2hpbGRyZW59XG4gICAgPC9UaGVtZVByb3ZpZGVyPlxuICApO1xufVxuIl0sIm5hbWVzIjpbIlRoZW1lUHJvdmlkZXIiLCJUaGVtZVdyYXBwZXIiLCJjaGlsZHJlbiJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ThemeWrapper.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/FuturisticNavbar.tsx":
/*!****************************************************!*\
  !*** ./src/components/layout/FuturisticNavbar.tsx ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ FuturisticNavbar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var _ui_FuturisticThemeToggle__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../ui/FuturisticThemeToggle */ \"(ssr)/./src/components/ui/FuturisticThemeToggle.tsx\");\n/* harmony import */ var _context_ThemeContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/context/ThemeContext */ \"(ssr)/./src/context/ThemeContext.tsx\");\n/* harmony import */ var _barrel_optimize_names_FiDollarSign_FiHome_FiLogOut_FiMenu_FiPieChart_FiSettings_FiTarget_FiUser_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=FiDollarSign,FiHome,FiLogOut,FiMenu,FiPieChart,FiSettings,FiTarget,FiUser,FiX!=!react-icons/fi */ \"(ssr)/./node_modules/react-icons/fi/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\nfunction FuturisticNavbar() {\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isScrolled, setIsScrolled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    const { theme } = (0,_context_ThemeContext__WEBPACK_IMPORTED_MODULE_5__.useTheme)();\n    const isDark = theme === 'dark';\n    // Check if user is logged in\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FuturisticNavbar.useEffect\": ()=>{\n            // Function to check user authentication status\n            const checkAuth = {\n                \"FuturisticNavbar.useEffect.checkAuth\": ()=>{\n                    const userData = localStorage.getItem('cashminder_user');\n                    if (userData) {\n                        setUser(JSON.parse(userData));\n                    } else {\n                        setUser(null);\n                    }\n                }\n            }[\"FuturisticNavbar.useEffect.checkAuth\"];\n            // Check on initial load\n            checkAuth();\n            // Set up event listener for storage changes (for when user logs in/out in another tab)\n            window.addEventListener('storage', checkAuth);\n            // Set up custom event listener for auth changes within the same tab\n            window.addEventListener('auth_state_changed', checkAuth);\n            // Check auth status every 5 seconds to ensure UI is in sync\n            const interval = setInterval(checkAuth, 5000);\n            return ({\n                \"FuturisticNavbar.useEffect\": ()=>{\n                    window.removeEventListener('storage', checkAuth);\n                    window.removeEventListener('auth_state_changed', checkAuth);\n                    clearInterval(interval);\n                }\n            })[\"FuturisticNavbar.useEffect\"];\n        }\n    }[\"FuturisticNavbar.useEffect\"], []);\n    // Handle scroll effect\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FuturisticNavbar.useEffect\": ()=>{\n            const handleScroll = {\n                \"FuturisticNavbar.useEffect.handleScroll\": ()=>{\n                    setIsScrolled(window.scrollY > 10);\n                }\n            }[\"FuturisticNavbar.useEffect.handleScroll\"];\n            window.addEventListener('scroll', handleScroll);\n            return ({\n                \"FuturisticNavbar.useEffect\": ()=>window.removeEventListener('scroll', handleScroll)\n            })[\"FuturisticNavbar.useEffect\"];\n        }\n    }[\"FuturisticNavbar.useEffect\"], []);\n    // Navigation items based on authentication status\n    const authenticatedNavItems = [\n        {\n            name: 'Dashboard',\n            href: '/dashboard',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiDollarSign_FiHome_FiLogOut_FiMenu_FiPieChart_FiSettings_FiTarget_FiUser_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__.FiHome, {\n                className: \"w-5 h-5\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\components\\\\layout\\\\FuturisticNavbar.tsx\",\n                lineNumber: 71,\n                columnNumber: 52\n            }, this)\n        },\n        {\n            name: 'Transactions',\n            href: '/transactions',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiDollarSign_FiHome_FiLogOut_FiMenu_FiPieChart_FiSettings_FiTarget_FiUser_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__.FiDollarSign, {\n                className: \"w-5 h-5\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\components\\\\layout\\\\FuturisticNavbar.tsx\",\n                lineNumber: 72,\n                columnNumber: 58\n            }, this)\n        },\n        {\n            name: 'Analytics',\n            href: '/analytics',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiDollarSign_FiHome_FiLogOut_FiMenu_FiPieChart_FiSettings_FiTarget_FiUser_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__.FiPieChart, {\n                className: \"w-5 h-5\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\components\\\\layout\\\\FuturisticNavbar.tsx\",\n                lineNumber: 73,\n                columnNumber: 52\n            }, this)\n        },\n        {\n            name: 'Goals',\n            href: '/goals',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiDollarSign_FiHome_FiLogOut_FiMenu_FiPieChart_FiSettings_FiTarget_FiUser_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__.FiTarget, {\n                className: \"w-5 h-5\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\components\\\\layout\\\\FuturisticNavbar.tsx\",\n                lineNumber: 74,\n                columnNumber: 44\n            }, this)\n        },\n        {\n            name: 'Settings',\n            href: '/settings',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiDollarSign_FiHome_FiLogOut_FiMenu_FiPieChart_FiSettings_FiTarget_FiUser_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__.FiSettings, {\n                className: \"w-5 h-5\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\components\\\\layout\\\\FuturisticNavbar.tsx\",\n                lineNumber: 75,\n                columnNumber: 50\n            }, this)\n        }\n    ];\n    const unauthenticatedNavItems = [\n        {\n            name: 'Home',\n            href: '/',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiDollarSign_FiHome_FiLogOut_FiMenu_FiPieChart_FiSettings_FiTarget_FiUser_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__.FiHome, {\n                className: \"w-5 h-5\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\components\\\\layout\\\\FuturisticNavbar.tsx\",\n                lineNumber: 79,\n                columnNumber: 38\n            }, this)\n        },\n        {\n            name: 'Features',\n            href: '/#features',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiDollarSign_FiHome_FiLogOut_FiMenu_FiPieChart_FiSettings_FiTarget_FiUser_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__.FiPieChart, {\n                className: \"w-5 h-5\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\components\\\\layout\\\\FuturisticNavbar.tsx\",\n                lineNumber: 80,\n                columnNumber: 51\n            }, this)\n        },\n        {\n            name: 'Pricing',\n            href: '/pricing',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiDollarSign_FiHome_FiLogOut_FiMenu_FiPieChart_FiSettings_FiTarget_FiUser_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__.FiDollarSign, {\n                className: \"w-5 h-5\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\components\\\\layout\\\\FuturisticNavbar.tsx\",\n                lineNumber: 81,\n                columnNumber: 48\n            }, this)\n        }\n    ];\n    // Use the appropriate navigation items based on authentication status\n    const navItems = user ? authenticatedNavItems : unauthenticatedNavItems;\n    const handleLogout = ()=>{\n        // Remove user data from localStorage\n        localStorage.removeItem('cashminder_user');\n        // Dispatch custom event to notify other components about auth state change\n        window.dispatchEvent(new Event('auth_state_changed'));\n        // Set user state to null\n        setUser(null);\n        // Redirect to home page\n        window.location.href = '/';\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.nav, {\n                className: `fixed top-0 left-0 right-0 z-50 ${isScrolled ? 'bg-light-bg/95 dark:bg-dark-bg/95 backdrop-blur-md border-b border-light-border dark:border-dark-border shadow-sm' : 'bg-transparent'} transition-all duration-300`,\n                initial: {\n                    y: -100\n                },\n                animate: {\n                    y: 0\n                },\n                transition: {\n                    type: 'spring',\n                    stiffness: 300,\n                    damping: 30\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-between h-16\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: \"/\",\n                                            className: \"flex-shrink-0 flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                                    className: \"w-10 h-10 rounded-lg bg-gradient-to-br from-primary to-secondary flex items-center justify-center mr-2 shadow-md\",\n                                                    whileHover: {\n                                                        scale: 1.05,\n                                                        rotate: 5\n                                                    },\n                                                    whileTap: {\n                                                        scale: 0.95\n                                                    },\n                                                    animate: {\n                                                        boxShadow: [\n                                                            '0 0 0 rgba(0, 198, 255, 0.4)',\n                                                            '0 0 15px rgba(0, 198, 255, 0.6)',\n                                                            '0 0 0 rgba(0, 198, 255, 0.4)'\n                                                        ]\n                                                    },\n                                                    transition: {\n                                                        duration: 2,\n                                                        repeat: Infinity,\n                                                        repeatType: \"reverse\"\n                                                    },\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.span, {\n                                                        className: \"text-dark-bg font-bold text-xl\",\n                                                        animate: {\n                                                            scale: [\n                                                                1,\n                                                                1.1,\n                                                                1\n                                                            ]\n                                                        },\n                                                        transition: {\n                                                            duration: 2,\n                                                            repeat: Infinity\n                                                        },\n                                                        children: \"C\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\components\\\\layout\\\\FuturisticNavbar.tsx\",\n                                                        lineNumber: 132,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\components\\\\layout\\\\FuturisticNavbar.tsx\",\n                                                    lineNumber: 119,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-bold text-xl text-light-text-primary dark:text-dark-text-primary\",\n                                                    children: \"Cashminder\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\components\\\\layout\\\\FuturisticNavbar.tsx\",\n                                                    lineNumber: 142,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\components\\\\layout\\\\FuturisticNavbar.tsx\",\n                                            lineNumber: 118,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"hidden md:ml-6 md:flex md:space-x-4\",\n                                            children: navItems.map((item)=>{\n                                                const isActive = pathname === item.href;\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: item.href,\n                                                    className: `relative px-3 py-2 rounded-md text-sm font-medium flex items-center space-x-1 transition-all duration-200 ${isActive ? 'text-primary bg-primary/10 dark:bg-primary/20 border border-primary/20 dark:border-primary/30' : 'text-light-text-secondary dark:text-dark-text-secondary hover:text-primary dark:hover:text-primary hover:bg-light-accent dark:hover:bg-dark-accent'}`,\n                                                    children: [\n                                                        item.icon,\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: item.name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\components\\\\layout\\\\FuturisticNavbar.tsx\",\n                                                            lineNumber: 162,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        isActive && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                                            className: \"absolute bottom-0 left-0 right-0 h-0.5 bg-primary\",\n                                                            layoutId: \"navbar-indicator\",\n                                                            initial: {\n                                                                opacity: 0\n                                                            },\n                                                            animate: {\n                                                                opacity: 1\n                                                            },\n                                                            transition: {\n                                                                type: 'spring',\n                                                                stiffness: 300,\n                                                                damping: 30\n                                                            },\n                                                            style: {\n                                                                boxShadow: '0 0 8px rgba(0, 198, 255, 0.6)'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\components\\\\layout\\\\FuturisticNavbar.tsx\",\n                                                            lineNumber: 164,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, item.name, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\components\\\\layout\\\\FuturisticNavbar.tsx\",\n                                                    lineNumber: 152,\n                                                    columnNumber: 21\n                                                }, this);\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\components\\\\layout\\\\FuturisticNavbar.tsx\",\n                                            lineNumber: 148,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\components\\\\layout\\\\FuturisticNavbar.tsx\",\n                                    lineNumber: 116,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_FuturisticThemeToggle__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\components\\\\layout\\\\FuturisticNavbar.tsx\",\n                                            lineNumber: 184,\n                                            columnNumber: 15\n                                        }, this),\n                                        user ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative flex items-center space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"hidden md:block text-sm font-medium text-light-text-secondary dark:text-dark-text-secondary\",\n                                                    children: [\n                                                        \"Hello, \",\n                                                        user.name || 'User'\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\components\\\\layout\\\\FuturisticNavbar.tsx\",\n                                                    lineNumber: 190,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.button, {\n                                                    className: \"flex items-center space-x-2 px-3 py-2 rounded-full bg-light-accent dark:bg-dark-accent border border-light-border dark:border-dark-border transition-all duration-200\",\n                                                    whileHover: {\n                                                        scale: 1.05,\n                                                        boxShadow: \"var(--card-shadow)\"\n                                                    },\n                                                    whileTap: {\n                                                        scale: 0.95\n                                                    },\n                                                    onClick: handleLogout,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiDollarSign_FiHome_FiLogOut_FiMenu_FiPieChart_FiSettings_FiTarget_FiUser_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__.FiLogOut, {\n                                                            className: \"w-5 h-5 text-primary\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\components\\\\layout\\\\FuturisticNavbar.tsx\",\n                                                            lineNumber: 204,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm font-medium text-light-text-primary dark:text-dark-text-primary\",\n                                                            children: \"Logout\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\components\\\\layout\\\\FuturisticNavbar.tsx\",\n                                                            lineNumber: 205,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\components\\\\layout\\\\FuturisticNavbar.tsx\",\n                                                    lineNumber: 195,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\components\\\\layout\\\\FuturisticNavbar.tsx\",\n                                            lineNumber: 188,\n                                            columnNumber: 17\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: \"/auth\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.button, {\n                                                className: \"flex items-center space-x-2 px-4 py-2 rounded-full bg-primary hover:bg-primary-400 text-dark-bg transition-all duration-200 shadow-md\",\n                                                whileHover: {\n                                                    scale: 1.05,\n                                                    boxShadow: \"var(--glow-primary)\"\n                                                },\n                                                whileTap: {\n                                                    scale: 0.95\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiDollarSign_FiHome_FiLogOut_FiMenu_FiPieChart_FiSettings_FiTarget_FiUser_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__.FiUser, {\n                                                        className: \"w-5 h-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\components\\\\layout\\\\FuturisticNavbar.tsx\",\n                                                        lineNumber: 218,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-medium\",\n                                                        children: \"Login\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\components\\\\layout\\\\FuturisticNavbar.tsx\",\n                                                        lineNumber: 219,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\components\\\\layout\\\\FuturisticNavbar.tsx\",\n                                                lineNumber: 210,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\components\\\\layout\\\\FuturisticNavbar.tsx\",\n                                            lineNumber: 209,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex md:hidden\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.button, {\n                                                className: `inline-flex items-center justify-center p-2 rounded-md ${isDark ? 'text-dark-text-secondary hover:text-dark-text-primary hover:bg-dark-surface' : 'text-light-text-secondary hover:text-light-text-primary hover:bg-light-border'}`,\n                                                whileTap: {\n                                                    scale: 0.9\n                                                },\n                                                onClick: ()=>setIsOpen(!isOpen),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"sr-only\",\n                                                        children: \"Open main menu\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\components\\\\layout\\\\FuturisticNavbar.tsx\",\n                                                        lineNumber: 235,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    isOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiDollarSign_FiHome_FiLogOut_FiMenu_FiPieChart_FiSettings_FiTarget_FiUser_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__.FiX, {\n                                                        className: \"block h-6 w-6\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\components\\\\layout\\\\FuturisticNavbar.tsx\",\n                                                        lineNumber: 237,\n                                                        columnNumber: 21\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiDollarSign_FiHome_FiLogOut_FiMenu_FiPieChart_FiSettings_FiTarget_FiUser_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__.FiMenu, {\n                                                        className: \"block h-6 w-6\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\components\\\\layout\\\\FuturisticNavbar.tsx\",\n                                                        lineNumber: 239,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\components\\\\layout\\\\FuturisticNavbar.tsx\",\n                                                lineNumber: 226,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\components\\\\layout\\\\FuturisticNavbar.tsx\",\n                                            lineNumber: 225,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\components\\\\layout\\\\FuturisticNavbar.tsx\",\n                                    lineNumber: 182,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\components\\\\layout\\\\FuturisticNavbar.tsx\",\n                            lineNumber: 115,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\components\\\\layout\\\\FuturisticNavbar.tsx\",\n                        lineNumber: 114,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.AnimatePresence, {\n                        children: isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                            className: \"md:hidden bg-light-card dark:bg-dark-card border-t border-light-border dark:border-dark-border shadow-lg glass-card\",\n                            initial: {\n                                opacity: 0,\n                                height: 0\n                            },\n                            animate: {\n                                opacity: 1,\n                                height: 'auto'\n                            },\n                            exit: {\n                                opacity: 0,\n                                height: 0\n                            },\n                            transition: {\n                                duration: 0.3\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"px-2 pt-2 pb-3 space-y-1 sm:px-3\",\n                                children: navItems.map((item)=>{\n                                    const isActive = pathname === item.href;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: item.href,\n                                        className: `px-3 py-2 rounded-md text-base font-medium flex items-center space-x-3 ${isActive ? 'text-primary bg-primary/10 dark:bg-primary/20 border border-primary/20 dark:border-primary/30' : 'text-light-text-secondary dark:text-dark-text-secondary hover:text-primary dark:hover:text-primary hover:bg-light-accent dark:hover:bg-dark-accent'}`,\n                                        onClick: ()=>setIsOpen(false),\n                                        children: [\n                                            item.icon,\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: item.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\components\\\\layout\\\\FuturisticNavbar.tsx\",\n                                                lineNumber: 272,\n                                                columnNumber: 23\n                                            }, this)\n                                        ]\n                                    }, item.name, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\components\\\\layout\\\\FuturisticNavbar.tsx\",\n                                        lineNumber: 261,\n                                        columnNumber: 21\n                                    }, this);\n                                })\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\components\\\\layout\\\\FuturisticNavbar.tsx\",\n                                lineNumber: 257,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\components\\\\layout\\\\FuturisticNavbar.tsx\",\n                            lineNumber: 250,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\components\\\\layout\\\\FuturisticNavbar.tsx\",\n                        lineNumber: 248,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\components\\\\layout\\\\FuturisticNavbar.tsx\",\n                lineNumber: 104,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"h-16\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\components\\\\layout\\\\FuturisticNavbar.tsx\",\n                lineNumber: 283,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/FuturisticNavbar.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/FuturisticThemeToggle.tsx":
/*!*****************************************************!*\
  !*** ./src/components/ui/FuturisticThemeToggle.tsx ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ FuturisticThemeToggle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _context_ThemeContext__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/context/ThemeContext */ \"(ssr)/./src/context/ThemeContext.tsx\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction FuturisticThemeToggle() {\n    const { theme, toggleTheme } = (0,_context_ThemeContext__WEBPACK_IMPORTED_MODULE_1__.useTheme)();\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isAnimating, setIsAnimating] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    // Only show the toggle after mounting to avoid hydration mismatch\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"FuturisticThemeToggle.useEffect\": ()=>{\n            setMounted(true);\n        }\n    }[\"FuturisticThemeToggle.useEffect\"], []);\n    // Handle the toggle click\n    const handleToggle = ()=>{\n        if (isAnimating) return;\n        setIsAnimating(true);\n        toggleTheme();\n        // Reset animation state after animation completes\n        setTimeout(()=>{\n            setIsAnimating(false);\n        }, 600);\n    };\n    // Don't render anything until mounted to prevent hydration issues\n    if (!mounted) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-12 h-12\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\components\\\\ui\\\\FuturisticThemeToggle.tsx\",\n            lineNumber: 32,\n            columnNumber: 12\n        }, this);\n    }\n    const isDark = theme === 'dark';\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n        className: \"relative w-12 h-12 flex items-center justify-center\",\n        initial: false,\n        animate: {\n            scale: isAnimating ? 1.1 : 1\n        },\n        transition: {\n            duration: 0.3\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.button, {\n                onClick: handleToggle,\n                className: `w-12 h-12 rounded-full relative overflow-hidden flex items-center justify-center ${isDark ? 'bg-dark-card border border-dark-border shadow-inner' : 'bg-light-card border border-light-border shadow-md'} transition-all duration-300`,\n                whileHover: {\n                    scale: 1.05\n                },\n                whileTap: {\n                    scale: 0.95\n                },\n                \"aria-label\": `Switch to ${isDark ? 'light' : 'dark'} mode`,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.AnimatePresence, {\n                    mode: \"wait\",\n                    children: isDark ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                        initial: {\n                            y: 20,\n                            opacity: 0\n                        },\n                        animate: {\n                            y: 0,\n                            opacity: 1\n                        },\n                        exit: {\n                            y: -20,\n                            opacity: 0\n                        },\n                        transition: {\n                            duration: 0.3\n                        },\n                        className: \"absolute inset-0 flex items-center justify-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                    className: \"w-6 h-6 rounded-full bg-gray-100\",\n                                    animate: {\n                                        boxShadow: [\n                                            \"0 0 0px 0px rgba(255,255,255,0.5)\",\n                                            \"0 0 10px 2px rgba(255,255,255,0.3)\"\n                                        ]\n                                    },\n                                    transition: {\n                                        duration: 2,\n                                        repeat: Infinity,\n                                        repeatType: \"reverse\"\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\components\\\\ui\\\\FuturisticThemeToggle.tsx\",\n                                    lineNumber: 67,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                    className: \"absolute w-1 h-1 rounded-full bg-blue-200 top-0 right-0\",\n                                    animate: {\n                                        opacity: [\n                                            0.5,\n                                            1,\n                                            0.5\n                                        ]\n                                    },\n                                    transition: {\n                                        duration: 1.5,\n                                        repeat: Infinity\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\components\\\\ui\\\\FuturisticThemeToggle.tsx\",\n                                    lineNumber: 78,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                    className: \"absolute w-1 h-1 rounded-full bg-blue-200 bottom-1 left-0\",\n                                    animate: {\n                                        opacity: [\n                                            0.7,\n                                            0.3,\n                                            0.7\n                                        ]\n                                    },\n                                    transition: {\n                                        duration: 2,\n                                        repeat: Infinity\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\components\\\\ui\\\\FuturisticThemeToggle.tsx\",\n                                    lineNumber: 83,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\components\\\\ui\\\\FuturisticThemeToggle.tsx\",\n                            lineNumber: 66,\n                            columnNumber: 15\n                        }, this)\n                    }, \"dark-icon\", false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\components\\\\ui\\\\FuturisticThemeToggle.tsx\",\n                        lineNumber: 57,\n                        columnNumber: 13\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                        initial: {\n                            y: -20,\n                            opacity: 0\n                        },\n                        animate: {\n                            y: 0,\n                            opacity: 1\n                        },\n                        exit: {\n                            y: 20,\n                            opacity: 0\n                        },\n                        transition: {\n                            duration: 0.3\n                        },\n                        className: \"absolute inset-0 flex items-center justify-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                    className: \"w-6 h-6 rounded-full bg-yellow-400\",\n                                    animate: {\n                                        boxShadow: [\n                                            \"0 0 0px 0px rgba(250,204,21,0.5)\",\n                                            \"0 0 10px 2px rgba(250,204,21,0.3)\"\n                                        ]\n                                    },\n                                    transition: {\n                                        duration: 2,\n                                        repeat: Infinity,\n                                        repeatType: \"reverse\"\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\components\\\\ui\\\\FuturisticThemeToggle.tsx\",\n                                    lineNumber: 101,\n                                    columnNumber: 17\n                                }, this),\n                                [\n                                    ...Array(8)\n                                ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                        className: \"absolute w-1 h-2 bg-yellow-400\",\n                                        style: {\n                                            left: '50%',\n                                            top: '50%',\n                                            marginLeft: '-0.5px',\n                                            marginTop: '-1px',\n                                            transformOrigin: '50% 0',\n                                            transform: `rotate(${i * 45}deg) translateY(-5px)`\n                                        },\n                                        animate: {\n                                            height: [\n                                                2,\n                                                3,\n                                                2\n                                            ],\n                                            opacity: [\n                                                0.8,\n                                                1,\n                                                0.8\n                                            ]\n                                        },\n                                        transition: {\n                                            duration: 1.5,\n                                            repeat: Infinity,\n                                            delay: i * 0.1\n                                        }\n                                    }, i, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\components\\\\ui\\\\FuturisticThemeToggle.tsx\",\n                                        lineNumber: 114,\n                                        columnNumber: 19\n                                    }, this))\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\components\\\\ui\\\\FuturisticThemeToggle.tsx\",\n                            lineNumber: 100,\n                            columnNumber: 15\n                        }, this)\n                    }, \"light-icon\", false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\components\\\\ui\\\\FuturisticThemeToggle.tsx\",\n                        lineNumber: 91,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\components\\\\ui\\\\FuturisticThemeToggle.tsx\",\n                    lineNumber: 55,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\components\\\\ui\\\\FuturisticThemeToggle.tsx\",\n                lineNumber: 44,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.AnimatePresence, {\n                children: isAnimating && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                    initial: {\n                        scale: 0,\n                        opacity: 0.7\n                    },\n                    animate: {\n                        scale: 4,\n                        opacity: 0\n                    },\n                    exit: {\n                        opacity: 0\n                    },\n                    transition: {\n                        duration: 0.6\n                    },\n                    className: `absolute inset-0 rounded-full ${isDark ? 'bg-primary' : 'bg-primary'}`,\n                    style: {\n                        zIndex: -1\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\components\\\\ui\\\\FuturisticThemeToggle.tsx\",\n                    lineNumber: 145,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\components\\\\ui\\\\FuturisticThemeToggle.tsx\",\n                lineNumber: 143,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\components\\\\ui\\\\FuturisticThemeToggle.tsx\",\n        lineNumber: 38,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/FuturisticThemeToggle.tsx\n");

/***/ }),

/***/ "(ssr)/./src/context/ThemeContext.tsx":
/*!**************************************!*\
  !*** ./src/context/ThemeContext.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ThemeProvider: () => (/* binding */ ThemeProvider),\n/* harmony export */   useTheme: () => (/* binding */ useTheme)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ ThemeProvider,useTheme auto */ \n\nconst ThemeContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction ThemeProvider({ children }) {\n    const [theme, setTheme] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('light');\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ThemeProvider.useEffect\": ()=>{\n            // This effect runs on client-side only\n            try {\n                // Check local storage first\n                const savedTheme = localStorage.getItem('theme');\n                let themeToApply;\n                if (savedTheme && (savedTheme === 'dark' || savedTheme === 'light')) {\n                    themeToApply = savedTheme;\n                } else {\n                    // If no saved theme, check system preference\n                    const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;\n                    themeToApply = prefersDark ? 'dark' : 'light';\n                    // Save the initial theme preference\n                    localStorage.setItem('theme', themeToApply);\n                }\n                // Update state\n                setTheme(themeToApply);\n                // Apply theme to DOM\n                if (themeToApply === 'dark') {\n                    document.documentElement.classList.add('dark');\n                    document.body.classList.add('dark');\n                    console.log('Dark mode initialized');\n                } else {\n                    document.documentElement.classList.remove('dark');\n                    document.body.classList.remove('dark');\n                    console.log('Light mode initialized');\n                }\n                console.log('Theme initialized to:', themeToApply);\n                console.log('Dark class present:', document.documentElement.classList.contains('dark'));\n            } catch (error) {\n                console.error('Failed to initialize theme:', error);\n            }\n        }\n    }[\"ThemeProvider.useEffect\"], []);\n    const toggleTheme = ()=>{\n        try {\n            const newTheme = theme === 'light' ? 'dark' : 'light';\n            setTheme(newTheme);\n            // Force toggle the dark class on the html element\n            if (newTheme === 'dark') {\n                document.documentElement.classList.add('dark');\n                document.body.classList.add('dark');\n                console.log('Dark mode enabled');\n            } else {\n                document.documentElement.classList.remove('dark');\n                document.body.classList.remove('dark');\n                console.log('Light mode enabled');\n            }\n            // Save to localStorage\n            localStorage.setItem('theme', newTheme);\n            // Log the current state for debugging\n            console.log('Theme toggled to:', newTheme);\n            console.log('Dark class present:', document.documentElement.classList.contains('dark'));\n        } catch (error) {\n            console.error('Failed to toggle theme:', error);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ThemeContext.Provider, {\n        value: {\n            theme,\n            toggleTheme\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\context\\\\ThemeContext.tsx\",\n        lineNumber: 83,\n        columnNumber: 5\n    }, this);\n}\nfunction useTheme() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(ThemeContext);\n    if (context === undefined) {\n        throw new Error('useTheme must be used within a ThemeProvider');\n    }\n    return context;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/context/ThemeContext.tsx\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/framer-motion","vendor-chunks/react-icons","vendor-chunks/motion-dom","vendor-chunks/motion-utils","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5CUsers%5Cshiva%5COneDrive%5CDesktop%5Cmk%5CCashminder---Money-Management-App%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cshiva%5COneDrive%5CDesktop%5Cmk%5CCashminder---Money-Management-App&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();