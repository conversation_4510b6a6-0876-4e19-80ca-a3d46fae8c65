{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_94a4b977._.js", "server/edge/chunks/[root of the server]__860be4f0._.js", "server/edge/chunks/edge-wrapper_1985d09c.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "NYAyTW8Qh+xTEp0LMAwr+/JIogYY2vq+j/IVhP2/7qA=", "__NEXT_PREVIEW_MODE_ID": "cda9d3c0fd2483c6f529df13e7b53624", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "400fc42f0c968b1717c9a3ed9329401a7edbd69e73b95e951a205cc8bf1d0b13", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "fc55ee81d94cd2840e914dbfab95f83b6671d1a6c614b27106e4fb7bf99ca4f9"}}}, "sortedMiddleware": ["/"], "functions": {}}