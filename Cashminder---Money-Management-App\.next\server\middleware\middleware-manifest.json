{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_e54e5f28._.js", "server/edge/chunks/[root of the server]__ff3593a8._.js", "server/edge/chunks/edge-wrapper_a395f0b6.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/api/:path*{(\\\\.json)}?", "originalSource": "/api/:path*"}, {"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!api/auth|_next/static|_next/image|favicon.ico).*){(\\\\.json)}?", "originalSource": "/((?!api/auth|_next/static|_next/image|favicon.ico).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "2YKJb4vSMFSwQI4XiTH5+fw8fdU0GgRvppO0wzmYc5w=", "__NEXT_PREVIEW_MODE_ID": "e44ae1d46d830e7891b9d19d69d4267f", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "dab7bceef620f295acccf4906029a982556301a26bd85fbf506763159c89264e", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "b7914294f99b66e5073f358296dd5b3be9a1cbd8149aff26f733daccbcf1e72b"}}}, "instrumentation": null, "functions": {}}