"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/motion-dom";
exports.ids = ["vendor-chunks/motion-dom"];
exports.modules = {

/***/ "(ssr)/./node_modules/motion-dom/dist/es/animation/GroupAnimation.mjs":
/*!**********************************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/animation/GroupAnimation.mjs ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GroupAnimation: () => (/* binding */ GroupAnimation)\n/* harmony export */ });\n/* harmony import */ var _utils_supports_scroll_timeline_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../utils/supports/scroll-timeline.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/utils/supports/scroll-timeline.mjs\");\n\n\nclass GroupAnimation {\n    constructor(animations) {\n        // Bound to accomodate common `return animation.stop` pattern\n        this.stop = () => this.runAll(\"stop\");\n        this.animations = animations.filter(Boolean);\n    }\n    get finished() {\n        return Promise.all(this.animations.map((animation) => animation.finished));\n    }\n    /**\n     * TODO: Filter out cancelled or stopped animations before returning\n     */\n    getAll(propName) {\n        return this.animations[0][propName];\n    }\n    setAll(propName, newValue) {\n        for (let i = 0; i < this.animations.length; i++) {\n            this.animations[i][propName] = newValue;\n        }\n    }\n    attachTimeline(timeline, fallback) {\n        const subscriptions = this.animations.map((animation) => {\n            if ((0,_utils_supports_scroll_timeline_mjs__WEBPACK_IMPORTED_MODULE_0__.supportsScrollTimeline)() && animation.attachTimeline) {\n                return animation.attachTimeline(timeline);\n            }\n            else if (typeof fallback === \"function\") {\n                return fallback(animation);\n            }\n        });\n        return () => {\n            subscriptions.forEach((cancel, i) => {\n                cancel && cancel();\n                this.animations[i].stop();\n            });\n        };\n    }\n    get time() {\n        return this.getAll(\"time\");\n    }\n    set time(time) {\n        this.setAll(\"time\", time);\n    }\n    get speed() {\n        return this.getAll(\"speed\");\n    }\n    set speed(speed) {\n        this.setAll(\"speed\", speed);\n    }\n    get startTime() {\n        return this.getAll(\"startTime\");\n    }\n    get duration() {\n        let max = 0;\n        for (let i = 0; i < this.animations.length; i++) {\n            max = Math.max(max, this.animations[i].duration);\n        }\n        return max;\n    }\n    runAll(methodName) {\n        this.animations.forEach((controls) => controls[methodName]());\n    }\n    flatten() {\n        this.runAll(\"flatten\");\n    }\n    play() {\n        this.runAll(\"play\");\n    }\n    pause() {\n        this.runAll(\"pause\");\n    }\n    cancel() {\n        this.runAll(\"cancel\");\n    }\n    complete() {\n        this.runAll(\"complete\");\n    }\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/motion-dom/dist/es/animation/GroupAnimation.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/motion-dom/dist/es/animation/GroupAnimationWithThen.mjs":
/*!******************************************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/animation/GroupAnimationWithThen.mjs ***!
  \******************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GroupAnimationWithThen: () => (/* binding */ GroupAnimationWithThen)\n/* harmony export */ });\n/* harmony import */ var _GroupAnimation_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./GroupAnimation.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/animation/GroupAnimation.mjs\");\n\n\nclass GroupAnimationWithThen extends _GroupAnimation_mjs__WEBPACK_IMPORTED_MODULE_0__.GroupAnimation {\n    then(onResolve, _onReject) {\n        return this.finished.finally(onResolve).then(() => { });\n    }\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbW90aW9uLWRvbS9kaXN0L2VzL2FuaW1hdGlvbi9Hcm91cEFuaW1hdGlvbldpdGhUaGVuLm1qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFzRDs7QUFFdEQscUNBQXFDLCtEQUFjO0FBQ25EO0FBQ0EsOERBQThEO0FBQzlEO0FBQ0E7O0FBRWtDIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHNoaXZhXFxPbmVEcml2ZVxcRGVza3RvcFxcbWtcXENhc2htaW5kZXItLS1Nb25leS1NYW5hZ2VtZW50LUFwcFxcbm9kZV9tb2R1bGVzXFxtb3Rpb24tZG9tXFxkaXN0XFxlc1xcYW5pbWF0aW9uXFxHcm91cEFuaW1hdGlvbldpdGhUaGVuLm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBHcm91cEFuaW1hdGlvbiB9IGZyb20gJy4vR3JvdXBBbmltYXRpb24ubWpzJztcblxuY2xhc3MgR3JvdXBBbmltYXRpb25XaXRoVGhlbiBleHRlbmRzIEdyb3VwQW5pbWF0aW9uIHtcbiAgICB0aGVuKG9uUmVzb2x2ZSwgX29uUmVqZWN0KSB7XG4gICAgICAgIHJldHVybiB0aGlzLmZpbmlzaGVkLmZpbmFsbHkob25SZXNvbHZlKS50aGVuKCgpID0+IHsgfSk7XG4gICAgfVxufVxuXG5leHBvcnQgeyBHcm91cEFuaW1hdGlvbldpdGhUaGVuIH07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/motion-dom/dist/es/animation/GroupAnimationWithThen.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/motion-dom/dist/es/animation/NativeAnimation.mjs":
/*!***********************************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/animation/NativeAnimation.mjs ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NativeAnimation: () => (/* binding */ NativeAnimation)\n/* harmony export */ });\n/* harmony import */ var motion_utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! motion-utils */ \"(ssr)/./node_modules/motion-utils/dist/es/index.mjs\");\n/* harmony import */ var _render_dom_style_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../render/dom/style.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/render/dom/style.mjs\");\n/* harmony import */ var _keyframes_get_final_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./keyframes/get-final.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/animation/keyframes/get-final.mjs\");\n/* harmony import */ var _keyframes_hydrate_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./keyframes/hydrate.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/animation/keyframes/hydrate.mjs\");\n/* harmony import */ var _waapi_start_waapi_animation_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./waapi/start-waapi-animation.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/animation/waapi/start-waapi-animation.mjs\");\n/* harmony import */ var _waapi_utils_apply_generator_mjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./waapi/utils/apply-generator.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/animation/waapi/utils/apply-generator.mjs\");\n\n\n\n\n\n\n\nconst animationMaps = new WeakMap();\nconst animationMapKey = (name, pseudoElement) => `${name}:${pseudoElement}`;\nfunction getAnimationMap(element) {\n    const map = animationMaps.get(element) || new Map();\n    animationMaps.set(element, map);\n    return map;\n}\n/**\n * NativeAnimation implements AnimationPlaybackControls for the browser's Web Animations API.\n */\nclass NativeAnimation {\n    constructor(options) {\n        /**\n         * If we already have an animation, we don't need to instantiate one\n         * and can just use this as a controls interface.\n         */\n        if (\"animation\" in options) {\n            this.animation = options.animation;\n            return;\n        }\n        const { element, name, keyframes: unresolvedKeyframes, pseudoElement, allowFlatten = false, } = options;\n        let { transition } = options;\n        this.allowFlatten = allowFlatten;\n        /**\n         * Stop any existing animations on the element before reading existing keyframes.\n         *\n         * TODO: Check for VisualElement before using animation state. This is a fallback\n         * for mini animate(). Do this when implementing NativeAnimationExtended.\n         */\n        const animationMap = getAnimationMap(element);\n        const key = animationMapKey(name, pseudoElement || \"\");\n        const currentAnimation = animationMap.get(key);\n        currentAnimation && currentAnimation.stop();\n        /**\n         * TODO: If these keyframes aren't correctly hydrated then we want to throw\n         * run an instant animation.\n         */\n        const keyframes = (0,_keyframes_hydrate_mjs__WEBPACK_IMPORTED_MODULE_3__.hydrateKeyframes)(element, name, unresolvedKeyframes, pseudoElement);\n        (0,motion_utils__WEBPACK_IMPORTED_MODULE_0__.invariant)(typeof transition.type !== \"string\", `animateMini doesn't support \"type\" as a string. Did you mean to import { spring } from \"motion\"?`);\n        transition = (0,_waapi_utils_apply_generator_mjs__WEBPACK_IMPORTED_MODULE_5__.applyGeneratorOptions)(transition);\n        this.animation = (0,_waapi_start_waapi_animation_mjs__WEBPACK_IMPORTED_MODULE_4__.startWaapiAnimation)(element, name, keyframes, transition, pseudoElement);\n        if (transition.autoplay === false) {\n            this.animation.pause();\n        }\n        this.removeAnimation = () => animationMap.delete(key);\n        this.animation.onfinish = () => {\n            if (!pseudoElement) {\n                _render_dom_style_mjs__WEBPACK_IMPORTED_MODULE_1__.style.set(element, name, (0,_keyframes_get_final_mjs__WEBPACK_IMPORTED_MODULE_2__.getFinalKeyframe)(keyframes, transition));\n            }\n            else {\n                this.commitStyles();\n            }\n            this.cancel();\n        };\n        /**\n         * TODO: Check for VisualElement before using animation state.\n         */\n        animationMap.set(key, this);\n    }\n    play() {\n        this.animation.play();\n    }\n    pause() {\n        this.animation.pause();\n    }\n    complete() {\n        this.animation.finish();\n    }\n    cancel() {\n        try {\n            this.animation.cancel();\n        }\n        catch (e) { }\n        this.removeAnimation();\n    }\n    stop() {\n        const { state } = this;\n        if (state === \"idle\" || state === \"finished\") {\n            return;\n        }\n        this.commitStyles();\n        this.cancel();\n    }\n    /**\n     * WAAPI doesn't natively have any interruption capabilities.\n     *\n     * In this method, we commit styles back to the DOM before cancelling\n     * the animation.\n     *\n     * This is designed to be overridden by NativeAnimationExtended, which\n     * will create a renderless JS animation and sample it twice to calculate\n     * its current value, \"previous\" value, and therefore allow\n     * Motion to also correctly calculate velocity for any subsequent animation\n     * while deferring the commit until the next animation frame.\n     */\n    commitStyles() {\n        this.animation.commitStyles?.();\n    }\n    get duration() {\n        console.log(this.animation.effect?.getComputedTiming());\n        const duration = this.animation.effect?.getComputedTiming().duration || 0;\n        return (0,motion_utils__WEBPACK_IMPORTED_MODULE_0__.millisecondsToSeconds)(Number(duration));\n    }\n    get time() {\n        return (0,motion_utils__WEBPACK_IMPORTED_MODULE_0__.millisecondsToSeconds)(Number(this.animation.currentTime) || 0);\n    }\n    set time(newTime) {\n        this.animation.currentTime = (0,motion_utils__WEBPACK_IMPORTED_MODULE_0__.secondsToMilliseconds)(newTime);\n    }\n    /**\n     * The playback speed of the animation.\n     * 1 = normal speed, 2 = double speed, 0.5 = half speed.\n     */\n    get speed() {\n        return this.animation.playbackRate;\n    }\n    set speed(newSpeed) {\n        this.animation.playbackRate = newSpeed;\n    }\n    get state() {\n        return this.animation.playState;\n    }\n    get startTime() {\n        return Number(this.animation.startTime);\n    }\n    get finished() {\n        return this.animation.finished;\n    }\n    flatten() {\n        if (this.allowFlatten) {\n            this.animation.effect?.updateTiming({ easing: \"linear\" });\n        }\n    }\n    /**\n     * Attaches a timeline to the animation, for instance the `ScrollTimeline`.\n     */\n    attachTimeline(timeline) {\n        this.animation.timeline = timeline;\n        this.animation.onfinish = null;\n        return motion_utils__WEBPACK_IMPORTED_MODULE_0__.noop;\n    }\n    /**\n     * Allows the animation to be awaited.\n     *\n     * @deprecated Use `finished` instead.\n     */\n    then(onResolve, onReject) {\n        return this.finished.then(onResolve).catch(onReject);\n    }\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/motion-dom/dist/es/animation/NativeAnimation.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/motion-dom/dist/es/animation/generators/utils/calc-duration.mjs":
/*!**************************************************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/animation/generators/utils/calc-duration.mjs ***!
  \**************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calcGeneratorDuration: () => (/* binding */ calcGeneratorDuration),\n/* harmony export */   maxGeneratorDuration: () => (/* binding */ maxGeneratorDuration)\n/* harmony export */ });\n/**\n * Implement a practical max duration for keyframe generation\n * to prevent infinite loops\n */\nconst maxGeneratorDuration = 20000;\nfunction calcGeneratorDuration(generator) {\n    let duration = 0;\n    const timeStep = 50;\n    let state = generator.next(duration);\n    while (!state.done && duration < maxGeneratorDuration) {\n        duration += timeStep;\n        state = generator.next(duration);\n    }\n    return duration >= maxGeneratorDuration ? Infinity : duration;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbW90aW9uLWRvbS9kaXN0L2VzL2FuaW1hdGlvbi9nZW5lcmF0b3JzL3V0aWxzL2NhbGMtZHVyYXRpb24ubWpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUV1RCIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxzaGl2YVxcT25lRHJpdmVcXERlc2t0b3BcXG1rXFxDYXNobWluZGVyLS0tTW9uZXktTWFuYWdlbWVudC1BcHBcXG5vZGVfbW9kdWxlc1xcbW90aW9uLWRvbVxcZGlzdFxcZXNcXGFuaW1hdGlvblxcZ2VuZXJhdG9yc1xcdXRpbHNcXGNhbGMtZHVyYXRpb24ubWpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogSW1wbGVtZW50IGEgcHJhY3RpY2FsIG1heCBkdXJhdGlvbiBmb3Iga2V5ZnJhbWUgZ2VuZXJhdGlvblxuICogdG8gcHJldmVudCBpbmZpbml0ZSBsb29wc1xuICovXG5jb25zdCBtYXhHZW5lcmF0b3JEdXJhdGlvbiA9IDIwMDAwO1xuZnVuY3Rpb24gY2FsY0dlbmVyYXRvckR1cmF0aW9uKGdlbmVyYXRvcikge1xuICAgIGxldCBkdXJhdGlvbiA9IDA7XG4gICAgY29uc3QgdGltZVN0ZXAgPSA1MDtcbiAgICBsZXQgc3RhdGUgPSBnZW5lcmF0b3IubmV4dChkdXJhdGlvbik7XG4gICAgd2hpbGUgKCFzdGF0ZS5kb25lICYmIGR1cmF0aW9uIDwgbWF4R2VuZXJhdG9yRHVyYXRpb24pIHtcbiAgICAgICAgZHVyYXRpb24gKz0gdGltZVN0ZXA7XG4gICAgICAgIHN0YXRlID0gZ2VuZXJhdG9yLm5leHQoZHVyYXRpb24pO1xuICAgIH1cbiAgICByZXR1cm4gZHVyYXRpb24gPj0gbWF4R2VuZXJhdG9yRHVyYXRpb24gPyBJbmZpbml0eSA6IGR1cmF0aW9uO1xufVxuXG5leHBvcnQgeyBjYWxjR2VuZXJhdG9yRHVyYXRpb24sIG1heEdlbmVyYXRvckR1cmF0aW9uIH07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/motion-dom/dist/es/animation/generators/utils/calc-duration.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/motion-dom/dist/es/animation/generators/utils/create-generator-easing.mjs":
/*!************************************************************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/animation/generators/utils/create-generator-easing.mjs ***!
  \************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createGeneratorEasing: () => (/* binding */ createGeneratorEasing)\n/* harmony export */ });\n/* harmony import */ var motion_utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! motion-utils */ \"(ssr)/./node_modules/motion-utils/dist/es/index.mjs\");\n/* harmony import */ var _calc_duration_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./calc-duration.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/animation/generators/utils/calc-duration.mjs\");\n\n\n\n/**\n * Create a progress => progress easing function from a generator.\n */\nfunction createGeneratorEasing(options, scale = 100, createGenerator) {\n    const generator = createGenerator({ ...options, keyframes: [0, scale] });\n    const duration = Math.min((0,_calc_duration_mjs__WEBPACK_IMPORTED_MODULE_1__.calcGeneratorDuration)(generator), _calc_duration_mjs__WEBPACK_IMPORTED_MODULE_1__.maxGeneratorDuration);\n    return {\n        type: \"keyframes\",\n        ease: (progress) => {\n            return generator.next(duration * progress).value / scale;\n        },\n        duration: (0,motion_utils__WEBPACK_IMPORTED_MODULE_0__.millisecondsToSeconds)(duration),\n    };\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbW90aW9uLWRvbS9kaXN0L2VzL2FuaW1hdGlvbi9nZW5lcmF0b3JzL3V0aWxzL2NyZWF0ZS1nZW5lcmF0b3ItZWFzaW5nLm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBcUQ7QUFDNkI7O0FBRWxGO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esd0NBQXdDLG1DQUFtQztBQUMzRSw4QkFBOEIseUVBQXFCLGFBQWEsb0VBQW9CO0FBQ3BGO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNULGtCQUFrQixtRUFBcUI7QUFDdkM7QUFDQTs7QUFFaUMiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcc2hpdmFcXE9uZURyaXZlXFxEZXNrdG9wXFxta1xcQ2FzaG1pbmRlci0tLU1vbmV5LU1hbmFnZW1lbnQtQXBwXFxub2RlX21vZHVsZXNcXG1vdGlvbi1kb21cXGRpc3RcXGVzXFxhbmltYXRpb25cXGdlbmVyYXRvcnNcXHV0aWxzXFxjcmVhdGUtZ2VuZXJhdG9yLWVhc2luZy5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgbWlsbGlzZWNvbmRzVG9TZWNvbmRzIH0gZnJvbSAnbW90aW9uLXV0aWxzJztcbmltcG9ydCB7IGNhbGNHZW5lcmF0b3JEdXJhdGlvbiwgbWF4R2VuZXJhdG9yRHVyYXRpb24gfSBmcm9tICcuL2NhbGMtZHVyYXRpb24ubWpzJztcblxuLyoqXG4gKiBDcmVhdGUgYSBwcm9ncmVzcyA9PiBwcm9ncmVzcyBlYXNpbmcgZnVuY3Rpb24gZnJvbSBhIGdlbmVyYXRvci5cbiAqL1xuZnVuY3Rpb24gY3JlYXRlR2VuZXJhdG9yRWFzaW5nKG9wdGlvbnMsIHNjYWxlID0gMTAwLCBjcmVhdGVHZW5lcmF0b3IpIHtcbiAgICBjb25zdCBnZW5lcmF0b3IgPSBjcmVhdGVHZW5lcmF0b3IoeyAuLi5vcHRpb25zLCBrZXlmcmFtZXM6IFswLCBzY2FsZV0gfSk7XG4gICAgY29uc3QgZHVyYXRpb24gPSBNYXRoLm1pbihjYWxjR2VuZXJhdG9yRHVyYXRpb24oZ2VuZXJhdG9yKSwgbWF4R2VuZXJhdG9yRHVyYXRpb24pO1xuICAgIHJldHVybiB7XG4gICAgICAgIHR5cGU6IFwia2V5ZnJhbWVzXCIsXG4gICAgICAgIGVhc2U6IChwcm9ncmVzcykgPT4ge1xuICAgICAgICAgICAgcmV0dXJuIGdlbmVyYXRvci5uZXh0KGR1cmF0aW9uICogcHJvZ3Jlc3MpLnZhbHVlIC8gc2NhbGU7XG4gICAgICAgIH0sXG4gICAgICAgIGR1cmF0aW9uOiBtaWxsaXNlY29uZHNUb1NlY29uZHMoZHVyYXRpb24pLFxuICAgIH07XG59XG5cbmV4cG9ydCB7IGNyZWF0ZUdlbmVyYXRvckVhc2luZyB9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/motion-dom/dist/es/animation/generators/utils/create-generator-easing.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/motion-dom/dist/es/animation/generators/utils/is-generator.mjs":
/*!*************************************************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/animation/generators/utils/is-generator.mjs ***!
  \*************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isGenerator: () => (/* binding */ isGenerator)\n/* harmony export */ });\nfunction isGenerator(type) {\n    return typeof type === \"function\" && \"applyToOptions\" in type;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbW90aW9uLWRvbS9kaXN0L2VzL2FuaW1hdGlvbi9nZW5lcmF0b3JzL3V0aWxzL2lzLWdlbmVyYXRvci5tanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0E7QUFDQTs7QUFFdUIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcc2hpdmFcXE9uZURyaXZlXFxEZXNrdG9wXFxta1xcQ2FzaG1pbmRlci0tLU1vbmV5LU1hbmFnZW1lbnQtQXBwXFxub2RlX21vZHVsZXNcXG1vdGlvbi1kb21cXGRpc3RcXGVzXFxhbmltYXRpb25cXGdlbmVyYXRvcnNcXHV0aWxzXFxpcy1nZW5lcmF0b3IubWpzIl0sInNvdXJjZXNDb250ZW50IjpbImZ1bmN0aW9uIGlzR2VuZXJhdG9yKHR5cGUpIHtcbiAgICByZXR1cm4gdHlwZW9mIHR5cGUgPT09IFwiZnVuY3Rpb25cIiAmJiBcImFwcGx5VG9PcHRpb25zXCIgaW4gdHlwZTtcbn1cblxuZXhwb3J0IHsgaXNHZW5lcmF0b3IgfTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/motion-dom/dist/es/animation/generators/utils/is-generator.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/motion-dom/dist/es/animation/keyframes/get-final.mjs":
/*!***************************************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/animation/keyframes/get-final.mjs ***!
  \***************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getFinalKeyframe: () => (/* binding */ getFinalKeyframe)\n/* harmony export */ });\nconst isNotNull = (value) => value !== null;\nfunction getFinalKeyframe(keyframes, { repeat, repeatType = \"loop\" }, finalKeyframe) {\n    const resolvedKeyframes = keyframes.filter(isNotNull);\n    const index = repeat && repeatType !== \"loop\" && repeat % 2 === 1\n        ? 0\n        : resolvedKeyframes.length - 1;\n    return !index || finalKeyframe === undefined\n        ? resolvedKeyframes[index]\n        : finalKeyframe;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbW90aW9uLWRvbS9kaXN0L2VzL2FuaW1hdGlvbi9rZXlmcmFtZXMvZ2V0LWZpbmFsLm1qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQSx1Q0FBdUMsNkJBQTZCO0FBQ3BFO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRTRCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHNoaXZhXFxPbmVEcml2ZVxcRGVza3RvcFxcbWtcXENhc2htaW5kZXItLS1Nb25leS1NYW5hZ2VtZW50LUFwcFxcbm9kZV9tb2R1bGVzXFxtb3Rpb24tZG9tXFxkaXN0XFxlc1xcYW5pbWF0aW9uXFxrZXlmcmFtZXNcXGdldC1maW5hbC5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiY29uc3QgaXNOb3ROdWxsID0gKHZhbHVlKSA9PiB2YWx1ZSAhPT0gbnVsbDtcbmZ1bmN0aW9uIGdldEZpbmFsS2V5ZnJhbWUoa2V5ZnJhbWVzLCB7IHJlcGVhdCwgcmVwZWF0VHlwZSA9IFwibG9vcFwiIH0sIGZpbmFsS2V5ZnJhbWUpIHtcbiAgICBjb25zdCByZXNvbHZlZEtleWZyYW1lcyA9IGtleWZyYW1lcy5maWx0ZXIoaXNOb3ROdWxsKTtcbiAgICBjb25zdCBpbmRleCA9IHJlcGVhdCAmJiByZXBlYXRUeXBlICE9PSBcImxvb3BcIiAmJiByZXBlYXQgJSAyID09PSAxXG4gICAgICAgID8gMFxuICAgICAgICA6IHJlc29sdmVkS2V5ZnJhbWVzLmxlbmd0aCAtIDE7XG4gICAgcmV0dXJuICFpbmRleCB8fCBmaW5hbEtleWZyYW1lID09PSB1bmRlZmluZWRcbiAgICAgICAgPyByZXNvbHZlZEtleWZyYW1lc1tpbmRleF1cbiAgICAgICAgOiBmaW5hbEtleWZyYW1lO1xufVxuXG5leHBvcnQgeyBnZXRGaW5hbEtleWZyYW1lIH07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/motion-dom/dist/es/animation/keyframes/get-final.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/motion-dom/dist/es/animation/keyframes/hydrate.mjs":
/*!*************************************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/animation/keyframes/hydrate.mjs ***!
  \*************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   hydrateKeyframes: () => (/* binding */ hydrateKeyframes)\n/* harmony export */ });\n/* harmony import */ var _render_dom_style_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../render/dom/style.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/render/dom/style.mjs\");\n/* harmony import */ var _waapi_supports_partial_keyframes_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../waapi/supports/partial-keyframes.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/animation/waapi/supports/partial-keyframes.mjs\");\n/* harmony import */ var _waapi_utils_px_values_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../waapi/utils/px-values.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/animation/waapi/utils/px-values.mjs\");\n\n\n\n\nfunction hydrateKeyframes(element, name, keyframes, pseudoElement) {\n    if (!Array.isArray(keyframes)) {\n        keyframes = [keyframes];\n    }\n    for (let i = 0; i < keyframes.length; i++) {\n        if (keyframes[i] === null) {\n            keyframes[i] =\n                i === 0 && !pseudoElement\n                    ? _render_dom_style_mjs__WEBPACK_IMPORTED_MODULE_0__.style.get(element, name)\n                    : keyframes[i - 1];\n        }\n        if (typeof keyframes[i] === \"number\" && _waapi_utils_px_values_mjs__WEBPACK_IMPORTED_MODULE_2__.pxValues.has(name)) {\n            keyframes[i] = keyframes[i] + \"px\";\n        }\n    }\n    if (!pseudoElement && !(0,_waapi_supports_partial_keyframes_mjs__WEBPACK_IMPORTED_MODULE_1__.supportsPartialKeyframes)() && keyframes.length < 2) {\n        keyframes.unshift(_render_dom_style_mjs__WEBPACK_IMPORTED_MODULE_0__.style.get(element, name));\n    }\n    return keyframes;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbW90aW9uLWRvbS9kaXN0L2VzL2FuaW1hdGlvbi9rZXlmcmFtZXMvaHlkcmF0ZS5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUFtRDtBQUNnQztBQUMzQjs7QUFFeEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQSxvQkFBb0Isc0JBQXNCO0FBQzFDO0FBQ0E7QUFDQTtBQUNBLHNCQUFzQix3REFBSztBQUMzQjtBQUNBO0FBQ0EsZ0RBQWdELGdFQUFRO0FBQ3hEO0FBQ0E7QUFDQTtBQUNBLDJCQUEyQiwrRkFBd0I7QUFDbkQsMEJBQTBCLHdEQUFLO0FBQy9CO0FBQ0E7QUFDQTs7QUFFNEIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcc2hpdmFcXE9uZURyaXZlXFxEZXNrdG9wXFxta1xcQ2FzaG1pbmRlci0tLU1vbmV5LU1hbmFnZW1lbnQtQXBwXFxub2RlX21vZHVsZXNcXG1vdGlvbi1kb21cXGRpc3RcXGVzXFxhbmltYXRpb25cXGtleWZyYW1lc1xcaHlkcmF0ZS5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgc3R5bGUgfSBmcm9tICcuLi8uLi9yZW5kZXIvZG9tL3N0eWxlLm1qcyc7XG5pbXBvcnQgeyBzdXBwb3J0c1BhcnRpYWxLZXlmcmFtZXMgfSBmcm9tICcuLi93YWFwaS9zdXBwb3J0cy9wYXJ0aWFsLWtleWZyYW1lcy5tanMnO1xuaW1wb3J0IHsgcHhWYWx1ZXMgfSBmcm9tICcuLi93YWFwaS91dGlscy9weC12YWx1ZXMubWpzJztcblxuZnVuY3Rpb24gaHlkcmF0ZUtleWZyYW1lcyhlbGVtZW50LCBuYW1lLCBrZXlmcmFtZXMsIHBzZXVkb0VsZW1lbnQpIHtcbiAgICBpZiAoIUFycmF5LmlzQXJyYXkoa2V5ZnJhbWVzKSkge1xuICAgICAgICBrZXlmcmFtZXMgPSBba2V5ZnJhbWVzXTtcbiAgICB9XG4gICAgZm9yIChsZXQgaSA9IDA7IGkgPCBrZXlmcmFtZXMubGVuZ3RoOyBpKyspIHtcbiAgICAgICAgaWYgKGtleWZyYW1lc1tpXSA9PT0gbnVsbCkge1xuICAgICAgICAgICAga2V5ZnJhbWVzW2ldID1cbiAgICAgICAgICAgICAgICBpID09PSAwICYmICFwc2V1ZG9FbGVtZW50XG4gICAgICAgICAgICAgICAgICAgID8gc3R5bGUuZ2V0KGVsZW1lbnQsIG5hbWUpXG4gICAgICAgICAgICAgICAgICAgIDoga2V5ZnJhbWVzW2kgLSAxXTtcbiAgICAgICAgfVxuICAgICAgICBpZiAodHlwZW9mIGtleWZyYW1lc1tpXSA9PT0gXCJudW1iZXJcIiAmJiBweFZhbHVlcy5oYXMobmFtZSkpIHtcbiAgICAgICAgICAgIGtleWZyYW1lc1tpXSA9IGtleWZyYW1lc1tpXSArIFwicHhcIjtcbiAgICAgICAgfVxuICAgIH1cbiAgICBpZiAoIXBzZXVkb0VsZW1lbnQgJiYgIXN1cHBvcnRzUGFydGlhbEtleWZyYW1lcygpICYmIGtleWZyYW1lcy5sZW5ndGggPCAyKSB7XG4gICAgICAgIGtleWZyYW1lcy51bnNoaWZ0KHN0eWxlLmdldChlbGVtZW50LCBuYW1lKSk7XG4gICAgfVxuICAgIHJldHVybiBrZXlmcmFtZXM7XG59XG5cbmV4cG9ydCB7IGh5ZHJhdGVLZXlmcmFtZXMgfTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/motion-dom/dist/es/animation/keyframes/hydrate.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/motion-dom/dist/es/animation/utils/get-value-transition.mjs":
/*!**********************************************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/animation/utils/get-value-transition.mjs ***!
  \**********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getValueTransition: () => (/* binding */ getValueTransition)\n/* harmony export */ });\nfunction getValueTransition(transition, key) {\n    return (transition?.[key] ??\n        transition?.[\"default\"] ??\n        transition);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbW90aW9uLWRvbS9kaXN0L2VzL2FuaW1hdGlvbi91dGlscy9nZXQtdmFsdWUtdHJhbnNpdGlvbi5tanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRThCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHNoaXZhXFxPbmVEcml2ZVxcRGVza3RvcFxcbWtcXENhc2htaW5kZXItLS1Nb25leS1NYW5hZ2VtZW50LUFwcFxcbm9kZV9tb2R1bGVzXFxtb3Rpb24tZG9tXFxkaXN0XFxlc1xcYW5pbWF0aW9uXFx1dGlsc1xcZ2V0LXZhbHVlLXRyYW5zaXRpb24ubWpzIl0sInNvdXJjZXNDb250ZW50IjpbImZ1bmN0aW9uIGdldFZhbHVlVHJhbnNpdGlvbih0cmFuc2l0aW9uLCBrZXkpIHtcbiAgICByZXR1cm4gKHRyYW5zaXRpb24/LltrZXldID8/XG4gICAgICAgIHRyYW5zaXRpb24/LltcImRlZmF1bHRcIl0gPz9cbiAgICAgICAgdHJhbnNpdGlvbik7XG59XG5cbmV4cG9ydCB7IGdldFZhbHVlVHJhbnNpdGlvbiB9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/motion-dom/dist/es/animation/utils/get-value-transition.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/motion-dom/dist/es/animation/waapi/easing/cubic-bezier.mjs":
/*!*********************************************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/animation/waapi/easing/cubic-bezier.mjs ***!
  \*********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cubicBezierAsString: () => (/* binding */ cubicBezierAsString)\n/* harmony export */ });\nconst cubicBezierAsString = ([a, b, c, d]) => `cubic-bezier(${a}, ${b}, ${c}, ${d})`;\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbW90aW9uLWRvbS9kaXN0L2VzL2FuaW1hdGlvbi93YWFwaS9lYXNpbmcvY3ViaWMtYmV6aWVyLm1qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsOERBQThELEVBQUUsSUFBSSxFQUFFLElBQUksRUFBRSxJQUFJLEVBQUU7O0FBRW5EIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHNoaXZhXFxPbmVEcml2ZVxcRGVza3RvcFxcbWtcXENhc2htaW5kZXItLS1Nb25leS1NYW5hZ2VtZW50LUFwcFxcbm9kZV9tb2R1bGVzXFxtb3Rpb24tZG9tXFxkaXN0XFxlc1xcYW5pbWF0aW9uXFx3YWFwaVxcZWFzaW5nXFxjdWJpYy1iZXppZXIubWpzIl0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IGN1YmljQmV6aWVyQXNTdHJpbmcgPSAoW2EsIGIsIGMsIGRdKSA9PiBgY3ViaWMtYmV6aWVyKCR7YX0sICR7Yn0sICR7Y30sICR7ZH0pYDtcblxuZXhwb3J0IHsgY3ViaWNCZXppZXJBc1N0cmluZyB9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/motion-dom/dist/es/animation/waapi/easing/cubic-bezier.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/motion-dom/dist/es/animation/waapi/easing/is-supported.mjs":
/*!*********************************************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/animation/waapi/easing/is-supported.mjs ***!
  \*********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isWaapiSupportedEasing: () => (/* binding */ isWaapiSupportedEasing)\n/* harmony export */ });\n/* harmony import */ var _utils_is_bezier_definition_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../utils/is-bezier-definition.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/utils/is-bezier-definition.mjs\");\n/* harmony import */ var _utils_supports_linear_easing_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../utils/supports/linear-easing.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/utils/supports/linear-easing.mjs\");\n/* harmony import */ var _supported_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./supported.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/animation/waapi/easing/supported.mjs\");\n\n\n\n\nfunction isWaapiSupportedEasing(easing) {\n    return Boolean((typeof easing === \"function\" && (0,_utils_supports_linear_easing_mjs__WEBPACK_IMPORTED_MODULE_1__.supportsLinearEasing)()) ||\n        !easing ||\n        (typeof easing === \"string\" &&\n            (easing in _supported_mjs__WEBPACK_IMPORTED_MODULE_2__.supportedWaapiEasing || (0,_utils_supports_linear_easing_mjs__WEBPACK_IMPORTED_MODULE_1__.supportsLinearEasing)())) ||\n        (0,_utils_is_bezier_definition_mjs__WEBPACK_IMPORTED_MODULE_0__.isBezierDefinition)(easing) ||\n        (Array.isArray(easing) && easing.every(isWaapiSupportedEasing)));\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbW90aW9uLWRvbS9kaXN0L2VzL2FuaW1hdGlvbi93YWFwaS9lYXNpbmcvaXMtc3VwcG9ydGVkLm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQTZFO0FBQ0k7QUFDMUI7O0FBRXZEO0FBQ0Esb0RBQW9ELHVGQUFvQjtBQUN4RTtBQUNBO0FBQ0EsdUJBQXVCLGdFQUFvQixJQUFJLHVGQUFvQjtBQUNuRSxRQUFRLG1GQUFrQjtBQUMxQjtBQUNBOztBQUVrQyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxzaGl2YVxcT25lRHJpdmVcXERlc2t0b3BcXG1rXFxDYXNobWluZGVyLS0tTW9uZXktTWFuYWdlbWVudC1BcHBcXG5vZGVfbW9kdWxlc1xcbW90aW9uLWRvbVxcZGlzdFxcZXNcXGFuaW1hdGlvblxcd2FhcGlcXGVhc2luZ1xcaXMtc3VwcG9ydGVkLm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBpc0JlemllckRlZmluaXRpb24gfSBmcm9tICcuLi8uLi8uLi91dGlscy9pcy1iZXppZXItZGVmaW5pdGlvbi5tanMnO1xuaW1wb3J0IHsgc3VwcG9ydHNMaW5lYXJFYXNpbmcgfSBmcm9tICcuLi8uLi8uLi91dGlscy9zdXBwb3J0cy9saW5lYXItZWFzaW5nLm1qcyc7XG5pbXBvcnQgeyBzdXBwb3J0ZWRXYWFwaUVhc2luZyB9IGZyb20gJy4vc3VwcG9ydGVkLm1qcyc7XG5cbmZ1bmN0aW9uIGlzV2FhcGlTdXBwb3J0ZWRFYXNpbmcoZWFzaW5nKSB7XG4gICAgcmV0dXJuIEJvb2xlYW4oKHR5cGVvZiBlYXNpbmcgPT09IFwiZnVuY3Rpb25cIiAmJiBzdXBwb3J0c0xpbmVhckVhc2luZygpKSB8fFxuICAgICAgICAhZWFzaW5nIHx8XG4gICAgICAgICh0eXBlb2YgZWFzaW5nID09PSBcInN0cmluZ1wiICYmXG4gICAgICAgICAgICAoZWFzaW5nIGluIHN1cHBvcnRlZFdhYXBpRWFzaW5nIHx8IHN1cHBvcnRzTGluZWFyRWFzaW5nKCkpKSB8fFxuICAgICAgICBpc0JlemllckRlZmluaXRpb24oZWFzaW5nKSB8fFxuICAgICAgICAoQXJyYXkuaXNBcnJheShlYXNpbmcpICYmIGVhc2luZy5ldmVyeShpc1dhYXBpU3VwcG9ydGVkRWFzaW5nKSkpO1xufVxuXG5leHBvcnQgeyBpc1dhYXBpU3VwcG9ydGVkRWFzaW5nIH07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/motion-dom/dist/es/animation/waapi/easing/is-supported.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/motion-dom/dist/es/animation/waapi/easing/map-easing.mjs":
/*!*******************************************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/animation/waapi/easing/map-easing.mjs ***!
  \*******************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   mapEasingToNativeEasing: () => (/* binding */ mapEasingToNativeEasing)\n/* harmony export */ });\n/* harmony import */ var _utils_is_bezier_definition_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../utils/is-bezier-definition.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/utils/is-bezier-definition.mjs\");\n/* harmony import */ var _utils_supports_linear_easing_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../utils/supports/linear-easing.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/utils/supports/linear-easing.mjs\");\n/* harmony import */ var _utils_linear_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../utils/linear.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/animation/waapi/utils/linear.mjs\");\n/* harmony import */ var _cubic_bezier_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./cubic-bezier.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/animation/waapi/easing/cubic-bezier.mjs\");\n/* harmony import */ var _supported_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./supported.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/animation/waapi/easing/supported.mjs\");\n\n\n\n\n\n\nfunction mapEasingToNativeEasing(easing, duration) {\n    if (!easing) {\n        return undefined;\n    }\n    else if (typeof easing === \"function\" && (0,_utils_supports_linear_easing_mjs__WEBPACK_IMPORTED_MODULE_1__.supportsLinearEasing)()) {\n        return (0,_utils_linear_mjs__WEBPACK_IMPORTED_MODULE_2__.generateLinearEasing)(easing, duration);\n    }\n    else if ((0,_utils_is_bezier_definition_mjs__WEBPACK_IMPORTED_MODULE_0__.isBezierDefinition)(easing)) {\n        return (0,_cubic_bezier_mjs__WEBPACK_IMPORTED_MODULE_3__.cubicBezierAsString)(easing);\n    }\n    else if (Array.isArray(easing)) {\n        return easing.map((segmentEasing) => mapEasingToNativeEasing(segmentEasing, duration) ||\n            _supported_mjs__WEBPACK_IMPORTED_MODULE_4__.supportedWaapiEasing.easeOut);\n    }\n    else {\n        return _supported_mjs__WEBPACK_IMPORTED_MODULE_4__.supportedWaapiEasing[easing];\n    }\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/motion-dom/dist/es/animation/waapi/easing/map-easing.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/motion-dom/dist/es/animation/waapi/easing/supported.mjs":
/*!******************************************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/animation/waapi/easing/supported.mjs ***!
  \******************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   supportedWaapiEasing: () => (/* binding */ supportedWaapiEasing)\n/* harmony export */ });\n/* harmony import */ var _cubic_bezier_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./cubic-bezier.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/animation/waapi/easing/cubic-bezier.mjs\");\n\n\nconst supportedWaapiEasing = {\n    linear: \"linear\",\n    ease: \"ease\",\n    easeIn: \"ease-in\",\n    easeOut: \"ease-out\",\n    easeInOut: \"ease-in-out\",\n    circIn: /*@__PURE__*/ (0,_cubic_bezier_mjs__WEBPACK_IMPORTED_MODULE_0__.cubicBezierAsString)([0, 0.65, 0.55, 1]),\n    circOut: /*@__PURE__*/ (0,_cubic_bezier_mjs__WEBPACK_IMPORTED_MODULE_0__.cubicBezierAsString)([0.55, 0, 1, 0.45]),\n    backIn: /*@__PURE__*/ (0,_cubic_bezier_mjs__WEBPACK_IMPORTED_MODULE_0__.cubicBezierAsString)([0.31, 0.01, 0.66, -0.59]),\n    backOut: /*@__PURE__*/ (0,_cubic_bezier_mjs__WEBPACK_IMPORTED_MODULE_0__.cubicBezierAsString)([0.33, 1.53, 0.69, 0.99]),\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbW90aW9uLWRvbS9kaXN0L2VzL2FuaW1hdGlvbi93YWFwaS9lYXNpbmcvc3VwcG9ydGVkLm1qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUF5RDs7QUFFekQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMEJBQTBCLHNFQUFtQjtBQUM3QywyQkFBMkIsc0VBQW1CO0FBQzlDLDBCQUEwQixzRUFBbUI7QUFDN0MsMkJBQTJCLHNFQUFtQjtBQUM5Qzs7QUFFZ0MiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcc2hpdmFcXE9uZURyaXZlXFxEZXNrdG9wXFxta1xcQ2FzaG1pbmRlci0tLU1vbmV5LU1hbmFnZW1lbnQtQXBwXFxub2RlX21vZHVsZXNcXG1vdGlvbi1kb21cXGRpc3RcXGVzXFxhbmltYXRpb25cXHdhYXBpXFxlYXNpbmdcXHN1cHBvcnRlZC5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgY3ViaWNCZXppZXJBc1N0cmluZyB9IGZyb20gJy4vY3ViaWMtYmV6aWVyLm1qcyc7XG5cbmNvbnN0IHN1cHBvcnRlZFdhYXBpRWFzaW5nID0ge1xuICAgIGxpbmVhcjogXCJsaW5lYXJcIixcbiAgICBlYXNlOiBcImVhc2VcIixcbiAgICBlYXNlSW46IFwiZWFzZS1pblwiLFxuICAgIGVhc2VPdXQ6IFwiZWFzZS1vdXRcIixcbiAgICBlYXNlSW5PdXQ6IFwiZWFzZS1pbi1vdXRcIixcbiAgICBjaXJjSW46IC8qQF9fUFVSRV9fKi8gY3ViaWNCZXppZXJBc1N0cmluZyhbMCwgMC42NSwgMC41NSwgMV0pLFxuICAgIGNpcmNPdXQ6IC8qQF9fUFVSRV9fKi8gY3ViaWNCZXppZXJBc1N0cmluZyhbMC41NSwgMCwgMSwgMC40NV0pLFxuICAgIGJhY2tJbjogLypAX19QVVJFX18qLyBjdWJpY0JlemllckFzU3RyaW5nKFswLjMxLCAwLjAxLCAwLjY2LCAtMC41OV0pLFxuICAgIGJhY2tPdXQ6IC8qQF9fUFVSRV9fKi8gY3ViaWNCZXppZXJBc1N0cmluZyhbMC4zMywgMS41MywgMC42OSwgMC45OV0pLFxufTtcblxuZXhwb3J0IHsgc3VwcG9ydGVkV2FhcGlFYXNpbmcgfTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/motion-dom/dist/es/animation/waapi/easing/supported.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/motion-dom/dist/es/animation/waapi/start-waapi-animation.mjs":
/*!***********************************************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/animation/waapi/start-waapi-animation.mjs ***!
  \***********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   startWaapiAnimation: () => (/* binding */ startWaapiAnimation)\n/* harmony export */ });\n/* harmony import */ var _stats_animation_count_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../stats/animation-count.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/stats/animation-count.mjs\");\n/* harmony import */ var _stats_buffer_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../stats/buffer.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/stats/buffer.mjs\");\n/* harmony import */ var _easing_map_easing_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./easing/map-easing.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/animation/waapi/easing/map-easing.mjs\");\n\n\n\n\nfunction startWaapiAnimation(element, valueName, keyframes, { delay = 0, duration = 300, repeat = 0, repeatType = \"loop\", ease = \"easeInOut\", times, } = {}, pseudoElement = undefined) {\n    const keyframeOptions = {\n        [valueName]: keyframes,\n    };\n    if (times)\n        keyframeOptions.offset = times;\n    const easing = (0,_easing_map_easing_mjs__WEBPACK_IMPORTED_MODULE_2__.mapEasingToNativeEasing)(ease, duration);\n    /**\n     * If this is an easing array, apply to keyframes, not animation as a whole\n     */\n    if (Array.isArray(easing))\n        keyframeOptions.easing = easing;\n    if (_stats_buffer_mjs__WEBPACK_IMPORTED_MODULE_1__.statsBuffer.value) {\n        _stats_animation_count_mjs__WEBPACK_IMPORTED_MODULE_0__.activeAnimations.waapi++;\n    }\n    const animation = element.animate(keyframeOptions, {\n        delay,\n        duration,\n        easing: !Array.isArray(easing) ? easing : \"linear\",\n        fill: \"both\",\n        iterations: repeat + 1,\n        direction: repeatType === \"reverse\" ? \"alternate\" : \"normal\",\n        pseudoElement,\n    });\n    if (_stats_buffer_mjs__WEBPACK_IMPORTED_MODULE_1__.statsBuffer.value) {\n        animation.finished.finally(() => {\n            _stats_animation_count_mjs__WEBPACK_IMPORTED_MODULE_0__.activeAnimations.waapi--;\n        });\n    }\n    return animation;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/motion-dom/dist/es/animation/waapi/start-waapi-animation.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/motion-dom/dist/es/animation/waapi/supports/partial-keyframes.mjs":
/*!****************************************************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/animation/waapi/supports/partial-keyframes.mjs ***!
  \****************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   supportsPartialKeyframes: () => (/* binding */ supportsPartialKeyframes)\n/* harmony export */ });\n/* harmony import */ var motion_utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! motion-utils */ \"(ssr)/./node_modules/motion-utils/dist/es/index.mjs\");\n\n\nconst supportsPartialKeyframes = /*@__PURE__*/ (0,motion_utils__WEBPACK_IMPORTED_MODULE_0__.memo)(() => {\n    try {\n        document.createElement(\"div\").animate({ opacity: [1] });\n    }\n    catch (e) {\n        return false;\n    }\n    return true;\n});\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbW90aW9uLWRvbS9kaXN0L2VzL2FuaW1hdGlvbi93YWFwaS9zdXBwb3J0cy9wYXJ0aWFsLWtleWZyYW1lcy5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBb0M7O0FBRXBDLCtDQUErQyxrREFBSTtBQUNuRDtBQUNBLGdEQUFnRCxjQUFjO0FBQzlEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDOztBQUVtQyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxzaGl2YVxcT25lRHJpdmVcXERlc2t0b3BcXG1rXFxDYXNobWluZGVyLS0tTW9uZXktTWFuYWdlbWVudC1BcHBcXG5vZGVfbW9kdWxlc1xcbW90aW9uLWRvbVxcZGlzdFxcZXNcXGFuaW1hdGlvblxcd2FhcGlcXHN1cHBvcnRzXFxwYXJ0aWFsLWtleWZyYW1lcy5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgbWVtbyB9IGZyb20gJ21vdGlvbi11dGlscyc7XG5cbmNvbnN0IHN1cHBvcnRzUGFydGlhbEtleWZyYW1lcyA9IC8qQF9fUFVSRV9fKi8gbWVtbygoKSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgICAgZG9jdW1lbnQuY3JlYXRlRWxlbWVudChcImRpdlwiKS5hbmltYXRlKHsgb3BhY2l0eTogWzFdIH0pO1xuICAgIH1cbiAgICBjYXRjaCAoZSkge1xuICAgICAgICByZXR1cm4gZmFsc2U7XG4gICAgfVxuICAgIHJldHVybiB0cnVlO1xufSk7XG5cbmV4cG9ydCB7IHN1cHBvcnRzUGFydGlhbEtleWZyYW1lcyB9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/motion-dom/dist/es/animation/waapi/supports/partial-keyframes.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/motion-dom/dist/es/animation/waapi/utils/apply-generator.mjs":
/*!***********************************************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/animation/waapi/utils/apply-generator.mjs ***!
  \***********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   applyGeneratorOptions: () => (/* binding */ applyGeneratorOptions)\n/* harmony export */ });\n/* harmony import */ var _generators_utils_is_generator_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../generators/utils/is-generator.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/animation/generators/utils/is-generator.mjs\");\n\n\nfunction applyGeneratorOptions({ type, ...options }) {\n    if ((0,_generators_utils_is_generator_mjs__WEBPACK_IMPORTED_MODULE_0__.isGenerator)(type)) {\n        return type.applyToOptions(options);\n    }\n    else {\n        options.duration ?? (options.duration = 300);\n        options.ease ?? (options.ease = \"easeOut\");\n    }\n    return options;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbW90aW9uLWRvbS9kaXN0L2VzL2FuaW1hdGlvbi93YWFwaS91dGlscy9hcHBseS1nZW5lcmF0b3IubWpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQXNFOztBQUV0RSxpQ0FBaUMsa0JBQWtCO0FBQ25ELFFBQVEsK0VBQVc7QUFDbkI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFaUMiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcc2hpdmFcXE9uZURyaXZlXFxEZXNrdG9wXFxta1xcQ2FzaG1pbmRlci0tLU1vbmV5LU1hbmFnZW1lbnQtQXBwXFxub2RlX21vZHVsZXNcXG1vdGlvbi1kb21cXGRpc3RcXGVzXFxhbmltYXRpb25cXHdhYXBpXFx1dGlsc1xcYXBwbHktZ2VuZXJhdG9yLm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBpc0dlbmVyYXRvciB9IGZyb20gJy4uLy4uL2dlbmVyYXRvcnMvdXRpbHMvaXMtZ2VuZXJhdG9yLm1qcyc7XG5cbmZ1bmN0aW9uIGFwcGx5R2VuZXJhdG9yT3B0aW9ucyh7IHR5cGUsIC4uLm9wdGlvbnMgfSkge1xuICAgIGlmIChpc0dlbmVyYXRvcih0eXBlKSkge1xuICAgICAgICByZXR1cm4gdHlwZS5hcHBseVRvT3B0aW9ucyhvcHRpb25zKTtcbiAgICB9XG4gICAgZWxzZSB7XG4gICAgICAgIG9wdGlvbnMuZHVyYXRpb24gPz8gKG9wdGlvbnMuZHVyYXRpb24gPSAzMDApO1xuICAgICAgICBvcHRpb25zLmVhc2UgPz8gKG9wdGlvbnMuZWFzZSA9IFwiZWFzZU91dFwiKTtcbiAgICB9XG4gICAgcmV0dXJuIG9wdGlvbnM7XG59XG5cbmV4cG9ydCB7IGFwcGx5R2VuZXJhdG9yT3B0aW9ucyB9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/motion-dom/dist/es/animation/waapi/utils/apply-generator.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/motion-dom/dist/es/animation/waapi/utils/attach-timeline.mjs":
/*!***********************************************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/animation/waapi/utils/attach-timeline.mjs ***!
  \***********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   attachTimeline: () => (/* binding */ attachTimeline)\n/* harmony export */ });\nfunction attachTimeline(animation, timeline) {\n    animation.timeline = timeline;\n    animation.onfinish = null;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbW90aW9uLWRvbS9kaXN0L2VzL2FuaW1hdGlvbi93YWFwaS91dGlscy9hdHRhY2gtdGltZWxpbmUubWpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTs7QUFFMEIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcc2hpdmFcXE9uZURyaXZlXFxEZXNrdG9wXFxta1xcQ2FzaG1pbmRlci0tLU1vbmV5LU1hbmFnZW1lbnQtQXBwXFxub2RlX21vZHVsZXNcXG1vdGlvbi1kb21cXGRpc3RcXGVzXFxhbmltYXRpb25cXHdhYXBpXFx1dGlsc1xcYXR0YWNoLXRpbWVsaW5lLm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJmdW5jdGlvbiBhdHRhY2hUaW1lbGluZShhbmltYXRpb24sIHRpbWVsaW5lKSB7XG4gICAgYW5pbWF0aW9uLnRpbWVsaW5lID0gdGltZWxpbmU7XG4gICAgYW5pbWF0aW9uLm9uZmluaXNoID0gbnVsbDtcbn1cblxuZXhwb3J0IHsgYXR0YWNoVGltZWxpbmUgfTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/motion-dom/dist/es/animation/waapi/utils/attach-timeline.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/motion-dom/dist/es/animation/waapi/utils/linear.mjs":
/*!**************************************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/animation/waapi/utils/linear.mjs ***!
  \**************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   generateLinearEasing: () => (/* binding */ generateLinearEasing)\n/* harmony export */ });\nconst generateLinearEasing = (easing, duration, // as milliseconds\nresolution = 10 // as milliseconds\n) => {\n    let points = \"\";\n    const numPoints = Math.max(Math.round(duration / resolution), 2);\n    for (let i = 0; i < numPoints; i++) {\n        points += easing(i / (numPoints - 1)) + \", \";\n    }\n    return `linear(${points.substring(0, points.length - 2)})`;\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbW90aW9uLWRvbS9kaXN0L2VzL2FuaW1hdGlvbi93YWFwaS91dGlscy9saW5lYXIubWpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esb0JBQW9CLGVBQWU7QUFDbkM7QUFDQTtBQUNBLHFCQUFxQix1Q0FBdUM7QUFDNUQ7O0FBRWdDIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHNoaXZhXFxPbmVEcml2ZVxcRGVza3RvcFxcbWtcXENhc2htaW5kZXItLS1Nb25leS1NYW5hZ2VtZW50LUFwcFxcbm9kZV9tb2R1bGVzXFxtb3Rpb24tZG9tXFxkaXN0XFxlc1xcYW5pbWF0aW9uXFx3YWFwaVxcdXRpbHNcXGxpbmVhci5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiY29uc3QgZ2VuZXJhdGVMaW5lYXJFYXNpbmcgPSAoZWFzaW5nLCBkdXJhdGlvbiwgLy8gYXMgbWlsbGlzZWNvbmRzXG5yZXNvbHV0aW9uID0gMTAgLy8gYXMgbWlsbGlzZWNvbmRzXG4pID0+IHtcbiAgICBsZXQgcG9pbnRzID0gXCJcIjtcbiAgICBjb25zdCBudW1Qb2ludHMgPSBNYXRoLm1heChNYXRoLnJvdW5kKGR1cmF0aW9uIC8gcmVzb2x1dGlvbiksIDIpO1xuICAgIGZvciAobGV0IGkgPSAwOyBpIDwgbnVtUG9pbnRzOyBpKyspIHtcbiAgICAgICAgcG9pbnRzICs9IGVhc2luZyhpIC8gKG51bVBvaW50cyAtIDEpKSArIFwiLCBcIjtcbiAgICB9XG4gICAgcmV0dXJuIGBsaW5lYXIoJHtwb2ludHMuc3Vic3RyaW5nKDAsIHBvaW50cy5sZW5ndGggLSAyKX0pYDtcbn07XG5cbmV4cG9ydCB7IGdlbmVyYXRlTGluZWFyRWFzaW5nIH07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/motion-dom/dist/es/animation/waapi/utils/linear.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/motion-dom/dist/es/animation/waapi/utils/px-values.mjs":
/*!*****************************************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/animation/waapi/utils/px-values.mjs ***!
  \*****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   pxValues: () => (/* binding */ pxValues)\n/* harmony export */ });\nconst pxValues = new Set([\n    // Border props\n    \"borderWidth\",\n    \"borderTopWidth\",\n    \"borderRightWidth\",\n    \"borderBottomWidth\",\n    \"borderLeftWidth\",\n    \"borderRadius\",\n    \"radius\",\n    \"borderTopLeftRadius\",\n    \"borderTopRightRadius\",\n    \"borderBottomRightRadius\",\n    \"borderBottomLeftRadius\",\n    // Positioning props\n    \"width\",\n    \"maxWidth\",\n    \"height\",\n    \"maxHeight\",\n    \"top\",\n    \"right\",\n    \"bottom\",\n    \"left\",\n    // Spacing props\n    \"padding\",\n    \"paddingTop\",\n    \"paddingRight\",\n    \"paddingBottom\",\n    \"paddingLeft\",\n    \"margin\",\n    \"marginTop\",\n    \"marginRight\",\n    \"marginBottom\",\n    \"marginLeft\",\n    // Misc\n    \"backgroundPositionX\",\n    \"backgroundPositionY\",\n]);\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbW90aW9uLWRvbS9kaXN0L2VzL2FuaW1hdGlvbi93YWFwaS91dGlscy9weC12YWx1ZXMubWpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFb0IiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcc2hpdmFcXE9uZURyaXZlXFxEZXNrdG9wXFxta1xcQ2FzaG1pbmRlci0tLU1vbmV5LU1hbmFnZW1lbnQtQXBwXFxub2RlX21vZHVsZXNcXG1vdGlvbi1kb21cXGRpc3RcXGVzXFxhbmltYXRpb25cXHdhYXBpXFx1dGlsc1xccHgtdmFsdWVzLm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCBweFZhbHVlcyA9IG5ldyBTZXQoW1xuICAgIC8vIEJvcmRlciBwcm9wc1xuICAgIFwiYm9yZGVyV2lkdGhcIixcbiAgICBcImJvcmRlclRvcFdpZHRoXCIsXG4gICAgXCJib3JkZXJSaWdodFdpZHRoXCIsXG4gICAgXCJib3JkZXJCb3R0b21XaWR0aFwiLFxuICAgIFwiYm9yZGVyTGVmdFdpZHRoXCIsXG4gICAgXCJib3JkZXJSYWRpdXNcIixcbiAgICBcInJhZGl1c1wiLFxuICAgIFwiYm9yZGVyVG9wTGVmdFJhZGl1c1wiLFxuICAgIFwiYm9yZGVyVG9wUmlnaHRSYWRpdXNcIixcbiAgICBcImJvcmRlckJvdHRvbVJpZ2h0UmFkaXVzXCIsXG4gICAgXCJib3JkZXJCb3R0b21MZWZ0UmFkaXVzXCIsXG4gICAgLy8gUG9zaXRpb25pbmcgcHJvcHNcbiAgICBcIndpZHRoXCIsXG4gICAgXCJtYXhXaWR0aFwiLFxuICAgIFwiaGVpZ2h0XCIsXG4gICAgXCJtYXhIZWlnaHRcIixcbiAgICBcInRvcFwiLFxuICAgIFwicmlnaHRcIixcbiAgICBcImJvdHRvbVwiLFxuICAgIFwibGVmdFwiLFxuICAgIC8vIFNwYWNpbmcgcHJvcHNcbiAgICBcInBhZGRpbmdcIixcbiAgICBcInBhZGRpbmdUb3BcIixcbiAgICBcInBhZGRpbmdSaWdodFwiLFxuICAgIFwicGFkZGluZ0JvdHRvbVwiLFxuICAgIFwicGFkZGluZ0xlZnRcIixcbiAgICBcIm1hcmdpblwiLFxuICAgIFwibWFyZ2luVG9wXCIsXG4gICAgXCJtYXJnaW5SaWdodFwiLFxuICAgIFwibWFyZ2luQm90dG9tXCIsXG4gICAgXCJtYXJnaW5MZWZ0XCIsXG4gICAgLy8gTWlzY1xuICAgIFwiYmFja2dyb3VuZFBvc2l0aW9uWFwiLFxuICAgIFwiYmFja2dyb3VuZFBvc2l0aW9uWVwiLFxuXSk7XG5cbmV4cG9ydCB7IHB4VmFsdWVzIH07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/motion-dom/dist/es/animation/waapi/utils/px-values.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/motion-dom/dist/es/frameloop/batcher.mjs":
/*!***************************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/frameloop/batcher.mjs ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createRenderBatcher: () => (/* binding */ createRenderBatcher)\n/* harmony export */ });\n/* harmony import */ var motion_utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! motion-utils */ \"(ssr)/./node_modules/motion-utils/dist/es/index.mjs\");\n/* harmony import */ var _order_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./order.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/frameloop/order.mjs\");\n/* harmony import */ var _render_step_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./render-step.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/frameloop/render-step.mjs\");\n\n\n\n\nconst maxElapsed = 40;\nfunction createRenderBatcher(scheduleNextBatch, allowKeepAlive) {\n    let runNextFrame = false;\n    let useDefaultElapsed = true;\n    const state = {\n        delta: 0.0,\n        timestamp: 0.0,\n        isProcessing: false,\n    };\n    const flagRunNextFrame = () => (runNextFrame = true);\n    const steps = _order_mjs__WEBPACK_IMPORTED_MODULE_1__.stepsOrder.reduce((acc, key) => {\n        acc[key] = (0,_render_step_mjs__WEBPACK_IMPORTED_MODULE_2__.createRenderStep)(flagRunNextFrame, allowKeepAlive ? key : undefined);\n        return acc;\n    }, {});\n    const { read, resolveKeyframes, update, preRender, render, postRender } = steps;\n    const processBatch = () => {\n        const timestamp = motion_utils__WEBPACK_IMPORTED_MODULE_0__.MotionGlobalConfig.useManualTiming\n            ? state.timestamp\n            : performance.now();\n        runNextFrame = false;\n        if (!motion_utils__WEBPACK_IMPORTED_MODULE_0__.MotionGlobalConfig.useManualTiming) {\n            state.delta = useDefaultElapsed\n                ? 1000 / 60\n                : Math.max(Math.min(timestamp - state.timestamp, maxElapsed), 1);\n        }\n        state.timestamp = timestamp;\n        state.isProcessing = true;\n        // Unrolled render loop for better per-frame performance\n        read.process(state);\n        resolveKeyframes.process(state);\n        update.process(state);\n        preRender.process(state);\n        render.process(state);\n        postRender.process(state);\n        state.isProcessing = false;\n        if (runNextFrame && allowKeepAlive) {\n            useDefaultElapsed = false;\n            scheduleNextBatch(processBatch);\n        }\n    };\n    const wake = () => {\n        runNextFrame = true;\n        useDefaultElapsed = true;\n        if (!state.isProcessing) {\n            scheduleNextBatch(processBatch);\n        }\n    };\n    const schedule = _order_mjs__WEBPACK_IMPORTED_MODULE_1__.stepsOrder.reduce((acc, key) => {\n        const step = steps[key];\n        acc[key] = (process, keepAlive = false, immediate = false) => {\n            if (!runNextFrame)\n                wake();\n            return step.schedule(process, keepAlive, immediate);\n        };\n        return acc;\n    }, {});\n    const cancel = (process) => {\n        for (let i = 0; i < _order_mjs__WEBPACK_IMPORTED_MODULE_1__.stepsOrder.length; i++) {\n            steps[_order_mjs__WEBPACK_IMPORTED_MODULE_1__.stepsOrder[i]].cancel(process);\n        }\n    };\n    return { schedule, cancel, state, steps };\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/motion-dom/dist/es/frameloop/batcher.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/motion-dom/dist/es/frameloop/frame.mjs":
/*!*************************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/frameloop/frame.mjs ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cancelFrame: () => (/* binding */ cancelFrame),\n/* harmony export */   frame: () => (/* binding */ frame),\n/* harmony export */   frameData: () => (/* binding */ frameData),\n/* harmony export */   frameSteps: () => (/* binding */ frameSteps)\n/* harmony export */ });\n/* harmony import */ var motion_utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! motion-utils */ \"(ssr)/./node_modules/motion-utils/dist/es/index.mjs\");\n/* harmony import */ var _batcher_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./batcher.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/frameloop/batcher.mjs\");\n\n\n\nconst { schedule: frame, cancel: cancelFrame, state: frameData, steps: frameSteps, } = /* @__PURE__ */ (0,_batcher_mjs__WEBPACK_IMPORTED_MODULE_1__.createRenderBatcher)(typeof requestAnimationFrame !== \"undefined\" ? requestAnimationFrame : motion_utils__WEBPACK_IMPORTED_MODULE_0__.noop, true);\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbW90aW9uLWRvbS9kaXN0L2VzL2ZyYW1lbG9vcC9mcmFtZS5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQW9DO0FBQ2dCOztBQUVwRCxRQUFRLDZFQUE2RSxrQkFBa0IsaUVBQW1CLHdFQUF3RSw4Q0FBSTs7QUFFakoiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcc2hpdmFcXE9uZURyaXZlXFxEZXNrdG9wXFxta1xcQ2FzaG1pbmRlci0tLU1vbmV5LU1hbmFnZW1lbnQtQXBwXFxub2RlX21vZHVsZXNcXG1vdGlvbi1kb21cXGRpc3RcXGVzXFxmcmFtZWxvb3BcXGZyYW1lLm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBub29wIH0gZnJvbSAnbW90aW9uLXV0aWxzJztcbmltcG9ydCB7IGNyZWF0ZVJlbmRlckJhdGNoZXIgfSBmcm9tICcuL2JhdGNoZXIubWpzJztcblxuY29uc3QgeyBzY2hlZHVsZTogZnJhbWUsIGNhbmNlbDogY2FuY2VsRnJhbWUsIHN0YXRlOiBmcmFtZURhdGEsIHN0ZXBzOiBmcmFtZVN0ZXBzLCB9ID0gLyogQF9fUFVSRV9fICovIGNyZWF0ZVJlbmRlckJhdGNoZXIodHlwZW9mIHJlcXVlc3RBbmltYXRpb25GcmFtZSAhPT0gXCJ1bmRlZmluZWRcIiA/IHJlcXVlc3RBbmltYXRpb25GcmFtZSA6IG5vb3AsIHRydWUpO1xuXG5leHBvcnQgeyBjYW5jZWxGcmFtZSwgZnJhbWUsIGZyYW1lRGF0YSwgZnJhbWVTdGVwcyB9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/motion-dom/dist/es/frameloop/frame.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/motion-dom/dist/es/frameloop/microtask.mjs":
/*!*****************************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/frameloop/microtask.mjs ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cancelMicrotask: () => (/* binding */ cancelMicrotask),\n/* harmony export */   microtask: () => (/* binding */ microtask)\n/* harmony export */ });\n/* harmony import */ var _batcher_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./batcher.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/frameloop/batcher.mjs\");\n\n\nconst { schedule: microtask, cancel: cancelMicrotask } = \n/* @__PURE__ */ (0,_batcher_mjs__WEBPACK_IMPORTED_MODULE_0__.createRenderBatcher)(queueMicrotask, false);\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbW90aW9uLWRvbS9kaXN0L2VzL2ZyYW1lbG9vcC9taWNyb3Rhc2subWpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFvRDs7QUFFcEQsUUFBUSwrQ0FBK0M7QUFDdkQsZ0JBQWdCLGlFQUFtQjs7QUFFRyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxzaGl2YVxcT25lRHJpdmVcXERlc2t0b3BcXG1rXFxDYXNobWluZGVyLS0tTW9uZXktTWFuYWdlbWVudC1BcHBcXG5vZGVfbW9kdWxlc1xcbW90aW9uLWRvbVxcZGlzdFxcZXNcXGZyYW1lbG9vcFxcbWljcm90YXNrLm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBjcmVhdGVSZW5kZXJCYXRjaGVyIH0gZnJvbSAnLi9iYXRjaGVyLm1qcyc7XG5cbmNvbnN0IHsgc2NoZWR1bGU6IG1pY3JvdGFzaywgY2FuY2VsOiBjYW5jZWxNaWNyb3Rhc2sgfSA9IFxuLyogQF9fUFVSRV9fICovIGNyZWF0ZVJlbmRlckJhdGNoZXIocXVldWVNaWNyb3Rhc2ssIGZhbHNlKTtcblxuZXhwb3J0IHsgY2FuY2VsTWljcm90YXNrLCBtaWNyb3Rhc2sgfTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/motion-dom/dist/es/frameloop/microtask.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/motion-dom/dist/es/frameloop/order.mjs":
/*!*************************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/frameloop/order.mjs ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   stepsOrder: () => (/* binding */ stepsOrder)\n/* harmony export */ });\nconst stepsOrder = [\n    \"read\", // Read\n    \"resolveKeyframes\", // Write/Read/Write/Read\n    \"update\", // Compute\n    \"preRender\", // Compute\n    \"render\", // Write\n    \"postRender\", // Compute\n];\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbW90aW9uLWRvbS9kaXN0L2VzL2ZyYW1lbG9vcC9vcmRlci5tanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRXNCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHNoaXZhXFxPbmVEcml2ZVxcRGVza3RvcFxcbWtcXENhc2htaW5kZXItLS1Nb25leS1NYW5hZ2VtZW50LUFwcFxcbm9kZV9tb2R1bGVzXFxtb3Rpb24tZG9tXFxkaXN0XFxlc1xcZnJhbWVsb29wXFxvcmRlci5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiY29uc3Qgc3RlcHNPcmRlciA9IFtcbiAgICBcInJlYWRcIiwgLy8gUmVhZFxuICAgIFwicmVzb2x2ZUtleWZyYW1lc1wiLCAvLyBXcml0ZS9SZWFkL1dyaXRlL1JlYWRcbiAgICBcInVwZGF0ZVwiLCAvLyBDb21wdXRlXG4gICAgXCJwcmVSZW5kZXJcIiwgLy8gQ29tcHV0ZVxuICAgIFwicmVuZGVyXCIsIC8vIFdyaXRlXG4gICAgXCJwb3N0UmVuZGVyXCIsIC8vIENvbXB1dGVcbl07XG5cbmV4cG9ydCB7IHN0ZXBzT3JkZXIgfTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/motion-dom/dist/es/frameloop/order.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/motion-dom/dist/es/frameloop/render-step.mjs":
/*!*******************************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/frameloop/render-step.mjs ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createRenderStep: () => (/* binding */ createRenderStep)\n/* harmony export */ });\n/* harmony import */ var _stats_buffer_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../stats/buffer.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/stats/buffer.mjs\");\n\n\nfunction createRenderStep(runNextFrame, stepName) {\n    /**\n     * We create and reuse two queues, one to queue jobs for the current frame\n     * and one for the next. We reuse to avoid triggering GC after x frames.\n     */\n    let thisFrame = new Set();\n    let nextFrame = new Set();\n    /**\n     * Track whether we're currently processing jobs in this step. This way\n     * we can decide whether to schedule new jobs for this frame or next.\n     */\n    let isProcessing = false;\n    let flushNextFrame = false;\n    /**\n     * A set of processes which were marked keepAlive when scheduled.\n     */\n    const toKeepAlive = new WeakSet();\n    let latestFrameData = {\n        delta: 0.0,\n        timestamp: 0.0,\n        isProcessing: false,\n    };\n    let numCalls = 0;\n    function triggerCallback(callback) {\n        if (toKeepAlive.has(callback)) {\n            step.schedule(callback);\n            runNextFrame();\n        }\n        numCalls++;\n        callback(latestFrameData);\n    }\n    const step = {\n        /**\n         * Schedule a process to run on the next frame.\n         */\n        schedule: (callback, keepAlive = false, immediate = false) => {\n            const addToCurrentFrame = immediate && isProcessing;\n            const queue = addToCurrentFrame ? thisFrame : nextFrame;\n            if (keepAlive)\n                toKeepAlive.add(callback);\n            if (!queue.has(callback))\n                queue.add(callback);\n            return callback;\n        },\n        /**\n         * Cancel the provided callback from running on the next frame.\n         */\n        cancel: (callback) => {\n            nextFrame.delete(callback);\n            toKeepAlive.delete(callback);\n        },\n        /**\n         * Execute all schedule callbacks.\n         */\n        process: (frameData) => {\n            latestFrameData = frameData;\n            /**\n             * If we're already processing we've probably been triggered by a flushSync\n             * inside an existing process. Instead of executing, mark flushNextFrame\n             * as true and ensure we flush the following frame at the end of this one.\n             */\n            if (isProcessing) {\n                flushNextFrame = true;\n                return;\n            }\n            isProcessing = true;\n            [thisFrame, nextFrame] = [nextFrame, thisFrame];\n            // Execute this frame\n            thisFrame.forEach(triggerCallback);\n            /**\n             * If we're recording stats then\n             */\n            if (stepName && _stats_buffer_mjs__WEBPACK_IMPORTED_MODULE_0__.statsBuffer.value) {\n                _stats_buffer_mjs__WEBPACK_IMPORTED_MODULE_0__.statsBuffer.value.frameloop[stepName].push(numCalls);\n            }\n            numCalls = 0;\n            // Clear the frame so no callbacks remain. This is to avoid\n            // memory leaks should this render step not run for a while.\n            thisFrame.clear();\n            isProcessing = false;\n            if (flushNextFrame) {\n                flushNextFrame = false;\n                step.process(frameData);\n            }\n        },\n    };\n    return step;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/motion-dom/dist/es/frameloop/render-step.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/motion-dom/dist/es/frameloop/sync-time.mjs":
/*!*****************************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/frameloop/sync-time.mjs ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   time: () => (/* binding */ time)\n/* harmony export */ });\n/* harmony import */ var motion_utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! motion-utils */ \"(ssr)/./node_modules/motion-utils/dist/es/index.mjs\");\n/* harmony import */ var _frame_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./frame.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/frameloop/frame.mjs\");\n\n\n\nlet now;\nfunction clearTime() {\n    now = undefined;\n}\n/**\n * An eventloop-synchronous alternative to performance.now().\n *\n * Ensures that time measurements remain consistent within a synchronous context.\n * Usually calling performance.now() twice within the same synchronous context\n * will return different values which isn't useful for animations when we're usually\n * trying to sync animations to the same frame.\n */\nconst time = {\n    now: () => {\n        if (now === undefined) {\n            time.set(_frame_mjs__WEBPACK_IMPORTED_MODULE_1__.frameData.isProcessing || motion_utils__WEBPACK_IMPORTED_MODULE_0__.MotionGlobalConfig.useManualTiming\n                ? _frame_mjs__WEBPACK_IMPORTED_MODULE_1__.frameData.timestamp\n                : performance.now());\n        }\n        return now;\n    },\n    set: (newTime) => {\n        now = newTime;\n        queueMicrotask(clearTime);\n    },\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbW90aW9uLWRvbS9kaXN0L2VzL2ZyYW1lbG9vcC9zeW5jLXRpbWUubWpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFrRDtBQUNWOztBQUV4QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxxQkFBcUIsaURBQVMsaUJBQWlCLDREQUFrQjtBQUNqRSxrQkFBa0IsaURBQVM7QUFDM0I7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDs7QUFFZ0IiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcc2hpdmFcXE9uZURyaXZlXFxEZXNrdG9wXFxta1xcQ2FzaG1pbmRlci0tLU1vbmV5LU1hbmFnZW1lbnQtQXBwXFxub2RlX21vZHVsZXNcXG1vdGlvbi1kb21cXGRpc3RcXGVzXFxmcmFtZWxvb3BcXHN5bmMtdGltZS5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgTW90aW9uR2xvYmFsQ29uZmlnIH0gZnJvbSAnbW90aW9uLXV0aWxzJztcbmltcG9ydCB7IGZyYW1lRGF0YSB9IGZyb20gJy4vZnJhbWUubWpzJztcblxubGV0IG5vdztcbmZ1bmN0aW9uIGNsZWFyVGltZSgpIHtcbiAgICBub3cgPSB1bmRlZmluZWQ7XG59XG4vKipcbiAqIEFuIGV2ZW50bG9vcC1zeW5jaHJvbm91cyBhbHRlcm5hdGl2ZSB0byBwZXJmb3JtYW5jZS5ub3coKS5cbiAqXG4gKiBFbnN1cmVzIHRoYXQgdGltZSBtZWFzdXJlbWVudHMgcmVtYWluIGNvbnNpc3RlbnQgd2l0aGluIGEgc3luY2hyb25vdXMgY29udGV4dC5cbiAqIFVzdWFsbHkgY2FsbGluZyBwZXJmb3JtYW5jZS5ub3coKSB0d2ljZSB3aXRoaW4gdGhlIHNhbWUgc3luY2hyb25vdXMgY29udGV4dFxuICogd2lsbCByZXR1cm4gZGlmZmVyZW50IHZhbHVlcyB3aGljaCBpc24ndCB1c2VmdWwgZm9yIGFuaW1hdGlvbnMgd2hlbiB3ZSdyZSB1c3VhbGx5XG4gKiB0cnlpbmcgdG8gc3luYyBhbmltYXRpb25zIHRvIHRoZSBzYW1lIGZyYW1lLlxuICovXG5jb25zdCB0aW1lID0ge1xuICAgIG5vdzogKCkgPT4ge1xuICAgICAgICBpZiAobm93ID09PSB1bmRlZmluZWQpIHtcbiAgICAgICAgICAgIHRpbWUuc2V0KGZyYW1lRGF0YS5pc1Byb2Nlc3NpbmcgfHwgTW90aW9uR2xvYmFsQ29uZmlnLnVzZU1hbnVhbFRpbWluZ1xuICAgICAgICAgICAgICAgID8gZnJhbWVEYXRhLnRpbWVzdGFtcFxuICAgICAgICAgICAgICAgIDogcGVyZm9ybWFuY2Uubm93KCkpO1xuICAgICAgICB9XG4gICAgICAgIHJldHVybiBub3c7XG4gICAgfSxcbiAgICBzZXQ6IChuZXdUaW1lKSA9PiB7XG4gICAgICAgIG5vdyA9IG5ld1RpbWU7XG4gICAgICAgIHF1ZXVlTWljcm90YXNrKGNsZWFyVGltZSk7XG4gICAgfSxcbn07XG5cbmV4cG9ydCB7IHRpbWUgfTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/motion-dom/dist/es/frameloop/sync-time.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/motion-dom/dist/es/gestures/drag/state/is-active.mjs":
/*!***************************************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/gestures/drag/state/is-active.mjs ***!
  \***************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isDragActive: () => (/* binding */ isDragActive),\n/* harmony export */   isDragging: () => (/* binding */ isDragging)\n/* harmony export */ });\nconst isDragging = {\n    x: false,\n    y: false,\n};\nfunction isDragActive() {\n    return isDragging.x || isDragging.y;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbW90aW9uLWRvbS9kaXN0L2VzL2dlc3R1cmVzL2RyYWcvc3RhdGUvaXMtYWN0aXZlLm1qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVvQyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxzaGl2YVxcT25lRHJpdmVcXERlc2t0b3BcXG1rXFxDYXNobWluZGVyLS0tTW9uZXktTWFuYWdlbWVudC1BcHBcXG5vZGVfbW9kdWxlc1xcbW90aW9uLWRvbVxcZGlzdFxcZXNcXGdlc3R1cmVzXFxkcmFnXFxzdGF0ZVxcaXMtYWN0aXZlLm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCBpc0RyYWdnaW5nID0ge1xuICAgIHg6IGZhbHNlLFxuICAgIHk6IGZhbHNlLFxufTtcbmZ1bmN0aW9uIGlzRHJhZ0FjdGl2ZSgpIHtcbiAgICByZXR1cm4gaXNEcmFnZ2luZy54IHx8IGlzRHJhZ2dpbmcueTtcbn1cblxuZXhwb3J0IHsgaXNEcmFnQWN0aXZlLCBpc0RyYWdnaW5nIH07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/motion-dom/dist/es/gestures/drag/state/is-active.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/motion-dom/dist/es/gestures/drag/state/set-active.mjs":
/*!****************************************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/gestures/drag/state/set-active.mjs ***!
  \****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   setDragLock: () => (/* binding */ setDragLock)\n/* harmony export */ });\n/* harmony import */ var _is_active_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./is-active.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/gestures/drag/state/is-active.mjs\");\n\n\nfunction setDragLock(axis) {\n    if (axis === \"x\" || axis === \"y\") {\n        if (_is_active_mjs__WEBPACK_IMPORTED_MODULE_0__.isDragging[axis]) {\n            return null;\n        }\n        else {\n            _is_active_mjs__WEBPACK_IMPORTED_MODULE_0__.isDragging[axis] = true;\n            return () => {\n                _is_active_mjs__WEBPACK_IMPORTED_MODULE_0__.isDragging[axis] = false;\n            };\n        }\n    }\n    else {\n        if (_is_active_mjs__WEBPACK_IMPORTED_MODULE_0__.isDragging.x || _is_active_mjs__WEBPACK_IMPORTED_MODULE_0__.isDragging.y) {\n            return null;\n        }\n        else {\n            _is_active_mjs__WEBPACK_IMPORTED_MODULE_0__.isDragging.x = _is_active_mjs__WEBPACK_IMPORTED_MODULE_0__.isDragging.y = true;\n            return () => {\n                _is_active_mjs__WEBPACK_IMPORTED_MODULE_0__.isDragging.x = _is_active_mjs__WEBPACK_IMPORTED_MODULE_0__.isDragging.y = false;\n            };\n        }\n    }\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbW90aW9uLWRvbS9kaXN0L2VzL2dlc3R1cmVzL2RyYWcvc3RhdGUvc2V0LWFjdGl2ZS5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBNkM7O0FBRTdDO0FBQ0E7QUFDQSxZQUFZLHNEQUFVO0FBQ3RCO0FBQ0E7QUFDQTtBQUNBLFlBQVksc0RBQVU7QUFDdEI7QUFDQSxnQkFBZ0Isc0RBQVU7QUFDMUI7QUFDQTtBQUNBO0FBQ0E7QUFDQSxZQUFZLHNEQUFVLE1BQU0sc0RBQVU7QUFDdEM7QUFDQTtBQUNBO0FBQ0EsWUFBWSxzREFBVSxLQUFLLHNEQUFVO0FBQ3JDO0FBQ0EsZ0JBQWdCLHNEQUFVLEtBQUssc0RBQVU7QUFDekM7QUFDQTtBQUNBO0FBQ0E7O0FBRXVCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHNoaXZhXFxPbmVEcml2ZVxcRGVza3RvcFxcbWtcXENhc2htaW5kZXItLS1Nb25leS1NYW5hZ2VtZW50LUFwcFxcbm9kZV9tb2R1bGVzXFxtb3Rpb24tZG9tXFxkaXN0XFxlc1xcZ2VzdHVyZXNcXGRyYWdcXHN0YXRlXFxzZXQtYWN0aXZlLm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBpc0RyYWdnaW5nIH0gZnJvbSAnLi9pcy1hY3RpdmUubWpzJztcblxuZnVuY3Rpb24gc2V0RHJhZ0xvY2soYXhpcykge1xuICAgIGlmIChheGlzID09PSBcInhcIiB8fCBheGlzID09PSBcInlcIikge1xuICAgICAgICBpZiAoaXNEcmFnZ2luZ1theGlzXSkge1xuICAgICAgICAgICAgcmV0dXJuIG51bGw7XG4gICAgICAgIH1cbiAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICBpc0RyYWdnaW5nW2F4aXNdID0gdHJ1ZTtcbiAgICAgICAgICAgIHJldHVybiAoKSA9PiB7XG4gICAgICAgICAgICAgICAgaXNEcmFnZ2luZ1theGlzXSA9IGZhbHNlO1xuICAgICAgICAgICAgfTtcbiAgICAgICAgfVxuICAgIH1cbiAgICBlbHNlIHtcbiAgICAgICAgaWYgKGlzRHJhZ2dpbmcueCB8fCBpc0RyYWdnaW5nLnkpIHtcbiAgICAgICAgICAgIHJldHVybiBudWxsO1xuICAgICAgICB9XG4gICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgaXNEcmFnZ2luZy54ID0gaXNEcmFnZ2luZy55ID0gdHJ1ZTtcbiAgICAgICAgICAgIHJldHVybiAoKSA9PiB7XG4gICAgICAgICAgICAgICAgaXNEcmFnZ2luZy54ID0gaXNEcmFnZ2luZy55ID0gZmFsc2U7XG4gICAgICAgICAgICB9O1xuICAgICAgICB9XG4gICAgfVxufVxuXG5leHBvcnQgeyBzZXREcmFnTG9jayB9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/motion-dom/dist/es/gestures/drag/state/set-active.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/motion-dom/dist/es/gestures/hover.mjs":
/*!************************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/gestures/hover.mjs ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   hover: () => (/* binding */ hover)\n/* harmony export */ });\n/* harmony import */ var _drag_state_is_active_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./drag/state/is-active.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/gestures/drag/state/is-active.mjs\");\n/* harmony import */ var _utils_setup_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils/setup.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/gestures/utils/setup.mjs\");\n\n\n\nfunction isValidHover(event) {\n    return !(event.pointerType === \"touch\" || (0,_drag_state_is_active_mjs__WEBPACK_IMPORTED_MODULE_0__.isDragActive)());\n}\n/**\n * Create a hover gesture. hover() is different to .addEventListener(\"pointerenter\")\n * in that it has an easier syntax, filters out polyfilled touch events, interoperates\n * with drag gestures, and automatically removes the \"pointerennd\" event listener when the hover ends.\n *\n * @public\n */\nfunction hover(elementOrSelector, onHoverStart, options = {}) {\n    const [elements, eventOptions, cancel] = (0,_utils_setup_mjs__WEBPACK_IMPORTED_MODULE_1__.setupGesture)(elementOrSelector, options);\n    const onPointerEnter = (enterEvent) => {\n        if (!isValidHover(enterEvent))\n            return;\n        const { target } = enterEvent;\n        const onHoverEnd = onHoverStart(target, enterEvent);\n        if (typeof onHoverEnd !== \"function\" || !target)\n            return;\n        const onPointerLeave = (leaveEvent) => {\n            if (!isValidHover(leaveEvent))\n                return;\n            onHoverEnd(leaveEvent);\n            target.removeEventListener(\"pointerleave\", onPointerLeave);\n        };\n        target.addEventListener(\"pointerleave\", onPointerLeave, eventOptions);\n    };\n    elements.forEach((element) => {\n        element.addEventListener(\"pointerenter\", onPointerEnter, eventOptions);\n    });\n    return cancel;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/motion-dom/dist/es/gestures/hover.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/motion-dom/dist/es/gestures/press/index.mjs":
/*!******************************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/gestures/press/index.mjs ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   press: () => (/* binding */ press)\n/* harmony export */ });\n/* harmony import */ var _drag_state_is_active_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../drag/state/is-active.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/gestures/drag/state/is-active.mjs\");\n/* harmony import */ var _utils_is_node_or_child_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils/is-node-or-child.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/gestures/utils/is-node-or-child.mjs\");\n/* harmony import */ var _utils_is_primary_pointer_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../utils/is-primary-pointer.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/gestures/utils/is-primary-pointer.mjs\");\n/* harmony import */ var _utils_setup_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../utils/setup.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/gestures/utils/setup.mjs\");\n/* harmony import */ var _utils_is_keyboard_accessible_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./utils/is-keyboard-accessible.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/gestures/press/utils/is-keyboard-accessible.mjs\");\n/* harmony import */ var _utils_keyboard_mjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./utils/keyboard.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/gestures/press/utils/keyboard.mjs\");\n/* harmony import */ var _utils_state_mjs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./utils/state.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/gestures/press/utils/state.mjs\");\n\n\n\n\n\n\n\n\n/**\n * Filter out events that are not primary pointer events, or are triggering\n * while a Motion gesture is active.\n */\nfunction isValidPressEvent(event) {\n    return (0,_utils_is_primary_pointer_mjs__WEBPACK_IMPORTED_MODULE_2__.isPrimaryPointer)(event) && !(0,_drag_state_is_active_mjs__WEBPACK_IMPORTED_MODULE_0__.isDragActive)();\n}\n/**\n * Create a press gesture.\n *\n * Press is different to `\"pointerdown\"`, `\"pointerup\"` in that it\n * automatically filters out secondary pointer events like right\n * click and multitouch.\n *\n * It also adds accessibility support for keyboards, where\n * an element with a press gesture will receive focus and\n *  trigger on Enter `\"keydown\"` and `\"keyup\"` events.\n *\n * This is different to a browser's `\"click\"` event, which does\n * respond to keyboards but only for the `\"click\"` itself, rather\n * than the press start and end/cancel. The element also needs\n * to be focusable for this to work, whereas a press gesture will\n * make an element focusable by default.\n *\n * @public\n */\nfunction press(targetOrSelector, onPressStart, options = {}) {\n    const [targets, eventOptions, cancelEvents] = (0,_utils_setup_mjs__WEBPACK_IMPORTED_MODULE_3__.setupGesture)(targetOrSelector, options);\n    const startPress = (startEvent) => {\n        const target = startEvent.currentTarget;\n        if (!isValidPressEvent(startEvent) || _utils_state_mjs__WEBPACK_IMPORTED_MODULE_6__.isPressing.has(target))\n            return;\n        _utils_state_mjs__WEBPACK_IMPORTED_MODULE_6__.isPressing.add(target);\n        const onPressEnd = onPressStart(target, startEvent);\n        const onPointerEnd = (endEvent, success) => {\n            window.removeEventListener(\"pointerup\", onPointerUp);\n            window.removeEventListener(\"pointercancel\", onPointerCancel);\n            if (!isValidPressEvent(endEvent) || !_utils_state_mjs__WEBPACK_IMPORTED_MODULE_6__.isPressing.has(target)) {\n                return;\n            }\n            _utils_state_mjs__WEBPACK_IMPORTED_MODULE_6__.isPressing.delete(target);\n            if (typeof onPressEnd === \"function\") {\n                onPressEnd(endEvent, { success });\n            }\n        };\n        const onPointerUp = (upEvent) => {\n            onPointerEnd(upEvent, target === window ||\n                target === document ||\n                options.useGlobalTarget ||\n                (0,_utils_is_node_or_child_mjs__WEBPACK_IMPORTED_MODULE_1__.isNodeOrChild)(target, upEvent.target));\n        };\n        const onPointerCancel = (cancelEvent) => {\n            onPointerEnd(cancelEvent, false);\n        };\n        window.addEventListener(\"pointerup\", onPointerUp, eventOptions);\n        window.addEventListener(\"pointercancel\", onPointerCancel, eventOptions);\n    };\n    targets.forEach((target) => {\n        const pointerDownTarget = options.useGlobalTarget ? window : target;\n        pointerDownTarget.addEventListener(\"pointerdown\", startPress, eventOptions);\n        if (target instanceof HTMLElement) {\n            target.addEventListener(\"focus\", (event) => (0,_utils_keyboard_mjs__WEBPACK_IMPORTED_MODULE_5__.enableKeyboardPress)(event, eventOptions));\n            if (!(0,_utils_is_keyboard_accessible_mjs__WEBPACK_IMPORTED_MODULE_4__.isElementKeyboardAccessible)(target) &&\n                !target.hasAttribute(\"tabindex\")) {\n                target.tabIndex = 0;\n            }\n        }\n    });\n    return cancelEvents;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/motion-dom/dist/es/gestures/press/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/motion-dom/dist/es/gestures/press/utils/is-keyboard-accessible.mjs":
/*!*****************************************************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/gestures/press/utils/is-keyboard-accessible.mjs ***!
  \*****************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isElementKeyboardAccessible: () => (/* binding */ isElementKeyboardAccessible)\n/* harmony export */ });\nconst focusableElements = new Set([\n    \"BUTTON\",\n    \"INPUT\",\n    \"SELECT\",\n    \"TEXTAREA\",\n    \"A\",\n]);\nfunction isElementKeyboardAccessible(element) {\n    return (focusableElements.has(element.tagName) ||\n        element.tabIndex !== -1);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbW90aW9uLWRvbS9kaXN0L2VzL2dlc3R1cmVzL3ByZXNzL3V0aWxzL2lzLWtleWJvYXJkLWFjY2Vzc2libGUubWpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUV1QyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxzaGl2YVxcT25lRHJpdmVcXERlc2t0b3BcXG1rXFxDYXNobWluZGVyLS0tTW9uZXktTWFuYWdlbWVudC1BcHBcXG5vZGVfbW9kdWxlc1xcbW90aW9uLWRvbVxcZGlzdFxcZXNcXGdlc3R1cmVzXFxwcmVzc1xcdXRpbHNcXGlzLWtleWJvYXJkLWFjY2Vzc2libGUubWpzIl0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IGZvY3VzYWJsZUVsZW1lbnRzID0gbmV3IFNldChbXG4gICAgXCJCVVRUT05cIixcbiAgICBcIklOUFVUXCIsXG4gICAgXCJTRUxFQ1RcIixcbiAgICBcIlRFWFRBUkVBXCIsXG4gICAgXCJBXCIsXG5dKTtcbmZ1bmN0aW9uIGlzRWxlbWVudEtleWJvYXJkQWNjZXNzaWJsZShlbGVtZW50KSB7XG4gICAgcmV0dXJuIChmb2N1c2FibGVFbGVtZW50cy5oYXMoZWxlbWVudC50YWdOYW1lKSB8fFxuICAgICAgICBlbGVtZW50LnRhYkluZGV4ICE9PSAtMSk7XG59XG5cbmV4cG9ydCB7IGlzRWxlbWVudEtleWJvYXJkQWNjZXNzaWJsZSB9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/motion-dom/dist/es/gestures/press/utils/is-keyboard-accessible.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/motion-dom/dist/es/gestures/press/utils/keyboard.mjs":
/*!***************************************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/gestures/press/utils/keyboard.mjs ***!
  \***************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   enableKeyboardPress: () => (/* binding */ enableKeyboardPress)\n/* harmony export */ });\n/* harmony import */ var _state_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./state.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/gestures/press/utils/state.mjs\");\n\n\n/**\n * Filter out events that are not \"Enter\" keys.\n */\nfunction filterEvents(callback) {\n    return (event) => {\n        if (event.key !== \"Enter\")\n            return;\n        callback(event);\n    };\n}\nfunction firePointerEvent(target, type) {\n    target.dispatchEvent(new PointerEvent(\"pointer\" + type, { isPrimary: true, bubbles: true }));\n}\nconst enableKeyboardPress = (focusEvent, eventOptions) => {\n    const element = focusEvent.currentTarget;\n    if (!element)\n        return;\n    const handleKeydown = filterEvents(() => {\n        if (_state_mjs__WEBPACK_IMPORTED_MODULE_0__.isPressing.has(element))\n            return;\n        firePointerEvent(element, \"down\");\n        const handleKeyup = filterEvents(() => {\n            firePointerEvent(element, \"up\");\n        });\n        const handleBlur = () => firePointerEvent(element, \"cancel\");\n        element.addEventListener(\"keyup\", handleKeyup, eventOptions);\n        element.addEventListener(\"blur\", handleBlur, eventOptions);\n    });\n    element.addEventListener(\"keydown\", handleKeydown, eventOptions);\n    /**\n     * Add an event listener that fires on blur to remove the keydown events.\n     */\n    element.addEventListener(\"blur\", () => element.removeEventListener(\"keydown\", handleKeydown), eventOptions);\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/motion-dom/dist/es/gestures/press/utils/keyboard.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/motion-dom/dist/es/gestures/press/utils/state.mjs":
/*!************************************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/gestures/press/utils/state.mjs ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isPressing: () => (/* binding */ isPressing)\n/* harmony export */ });\nconst isPressing = new WeakSet();\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbW90aW9uLWRvbS9kaXN0L2VzL2dlc3R1cmVzL3ByZXNzL3V0aWxzL3N0YXRlLm1qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7O0FBRXNCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHNoaXZhXFxPbmVEcml2ZVxcRGVza3RvcFxcbWtcXENhc2htaW5kZXItLS1Nb25leS1NYW5hZ2VtZW50LUFwcFxcbm9kZV9tb2R1bGVzXFxtb3Rpb24tZG9tXFxkaXN0XFxlc1xcZ2VzdHVyZXNcXHByZXNzXFx1dGlsc1xcc3RhdGUubWpzIl0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IGlzUHJlc3NpbmcgPSBuZXcgV2Vha1NldCgpO1xuXG5leHBvcnQgeyBpc1ByZXNzaW5nIH07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/motion-dom/dist/es/gestures/press/utils/state.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/motion-dom/dist/es/gestures/utils/is-node-or-child.mjs":
/*!*****************************************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/gestures/utils/is-node-or-child.mjs ***!
  \*****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isNodeOrChild: () => (/* binding */ isNodeOrChild)\n/* harmony export */ });\n/**\n * Recursively traverse up the tree to check whether the provided child node\n * is the parent or a descendant of it.\n *\n * @param parent - Element to find\n * @param child - Element to test against parent\n */\nconst isNodeOrChild = (parent, child) => {\n    if (!child) {\n        return false;\n    }\n    else if (parent === child) {\n        return true;\n    }\n    else {\n        return isNodeOrChild(parent, child.parentElement);\n    }\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbW90aW9uLWRvbS9kaXN0L2VzL2dlc3R1cmVzL3V0aWxzL2lzLW5vZGUtb3ItY2hpbGQubWpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRXlCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHNoaXZhXFxPbmVEcml2ZVxcRGVza3RvcFxcbWtcXENhc2htaW5kZXItLS1Nb25leS1NYW5hZ2VtZW50LUFwcFxcbm9kZV9tb2R1bGVzXFxtb3Rpb24tZG9tXFxkaXN0XFxlc1xcZ2VzdHVyZXNcXHV0aWxzXFxpcy1ub2RlLW9yLWNoaWxkLm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIFJlY3Vyc2l2ZWx5IHRyYXZlcnNlIHVwIHRoZSB0cmVlIHRvIGNoZWNrIHdoZXRoZXIgdGhlIHByb3ZpZGVkIGNoaWxkIG5vZGVcbiAqIGlzIHRoZSBwYXJlbnQgb3IgYSBkZXNjZW5kYW50IG9mIGl0LlxuICpcbiAqIEBwYXJhbSBwYXJlbnQgLSBFbGVtZW50IHRvIGZpbmRcbiAqIEBwYXJhbSBjaGlsZCAtIEVsZW1lbnQgdG8gdGVzdCBhZ2FpbnN0IHBhcmVudFxuICovXG5jb25zdCBpc05vZGVPckNoaWxkID0gKHBhcmVudCwgY2hpbGQpID0+IHtcbiAgICBpZiAoIWNoaWxkKSB7XG4gICAgICAgIHJldHVybiBmYWxzZTtcbiAgICB9XG4gICAgZWxzZSBpZiAocGFyZW50ID09PSBjaGlsZCkge1xuICAgICAgICByZXR1cm4gdHJ1ZTtcbiAgICB9XG4gICAgZWxzZSB7XG4gICAgICAgIHJldHVybiBpc05vZGVPckNoaWxkKHBhcmVudCwgY2hpbGQucGFyZW50RWxlbWVudCk7XG4gICAgfVxufTtcblxuZXhwb3J0IHsgaXNOb2RlT3JDaGlsZCB9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/motion-dom/dist/es/gestures/utils/is-node-or-child.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/motion-dom/dist/es/gestures/utils/is-primary-pointer.mjs":
/*!*******************************************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/gestures/utils/is-primary-pointer.mjs ***!
  \*******************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isPrimaryPointer: () => (/* binding */ isPrimaryPointer)\n/* harmony export */ });\nconst isPrimaryPointer = (event) => {\n    if (event.pointerType === \"mouse\") {\n        return typeof event.button !== \"number\" || event.button <= 0;\n    }\n    else {\n        /**\n         * isPrimary is true for all mice buttons, whereas every touch point\n         * is regarded as its own input. So subsequent concurrent touch points\n         * will be false.\n         *\n         * Specifically match against false here as incomplete versions of\n         * PointerEvents in very old browser might have it set as undefined.\n         */\n        return event.isPrimary !== false;\n    }\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbW90aW9uLWRvbS9kaXN0L2VzL2dlc3R1cmVzL3V0aWxzL2lzLXByaW1hcnktcG9pbnRlci5tanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUU0QiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxzaGl2YVxcT25lRHJpdmVcXERlc2t0b3BcXG1rXFxDYXNobWluZGVyLS0tTW9uZXktTWFuYWdlbWVudC1BcHBcXG5vZGVfbW9kdWxlc1xcbW90aW9uLWRvbVxcZGlzdFxcZXNcXGdlc3R1cmVzXFx1dGlsc1xcaXMtcHJpbWFyeS1wb2ludGVyLm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCBpc1ByaW1hcnlQb2ludGVyID0gKGV2ZW50KSA9PiB7XG4gICAgaWYgKGV2ZW50LnBvaW50ZXJUeXBlID09PSBcIm1vdXNlXCIpIHtcbiAgICAgICAgcmV0dXJuIHR5cGVvZiBldmVudC5idXR0b24gIT09IFwibnVtYmVyXCIgfHwgZXZlbnQuYnV0dG9uIDw9IDA7XG4gICAgfVxuICAgIGVsc2Uge1xuICAgICAgICAvKipcbiAgICAgICAgICogaXNQcmltYXJ5IGlzIHRydWUgZm9yIGFsbCBtaWNlIGJ1dHRvbnMsIHdoZXJlYXMgZXZlcnkgdG91Y2ggcG9pbnRcbiAgICAgICAgICogaXMgcmVnYXJkZWQgYXMgaXRzIG93biBpbnB1dC4gU28gc3Vic2VxdWVudCBjb25jdXJyZW50IHRvdWNoIHBvaW50c1xuICAgICAgICAgKiB3aWxsIGJlIGZhbHNlLlxuICAgICAgICAgKlxuICAgICAgICAgKiBTcGVjaWZpY2FsbHkgbWF0Y2ggYWdhaW5zdCBmYWxzZSBoZXJlIGFzIGluY29tcGxldGUgdmVyc2lvbnMgb2ZcbiAgICAgICAgICogUG9pbnRlckV2ZW50cyBpbiB2ZXJ5IG9sZCBicm93c2VyIG1pZ2h0IGhhdmUgaXQgc2V0IGFzIHVuZGVmaW5lZC5cbiAgICAgICAgICovXG4gICAgICAgIHJldHVybiBldmVudC5pc1ByaW1hcnkgIT09IGZhbHNlO1xuICAgIH1cbn07XG5cbmV4cG9ydCB7IGlzUHJpbWFyeVBvaW50ZXIgfTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/motion-dom/dist/es/gestures/utils/is-primary-pointer.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/motion-dom/dist/es/gestures/utils/setup.mjs":
/*!******************************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/gestures/utils/setup.mjs ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   setupGesture: () => (/* binding */ setupGesture)\n/* harmony export */ });\n/* harmony import */ var _utils_resolve_elements_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../utils/resolve-elements.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/utils/resolve-elements.mjs\");\n\n\nfunction setupGesture(elementOrSelector, options) {\n    const elements = (0,_utils_resolve_elements_mjs__WEBPACK_IMPORTED_MODULE_0__.resolveElements)(elementOrSelector);\n    const gestureAbortController = new AbortController();\n    const eventOptions = {\n        passive: true,\n        ...options,\n        signal: gestureAbortController.signal,\n    };\n    const cancel = () => gestureAbortController.abort();\n    return [elements, eventOptions, cancel];\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbW90aW9uLWRvbS9kaXN0L2VzL2dlc3R1cmVzL3V0aWxzL3NldHVwLm1qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFtRTs7QUFFbkU7QUFDQSxxQkFBcUIsNEVBQWU7QUFDcEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUV3QiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxzaGl2YVxcT25lRHJpdmVcXERlc2t0b3BcXG1rXFxDYXNobWluZGVyLS0tTW9uZXktTWFuYWdlbWVudC1BcHBcXG5vZGVfbW9kdWxlc1xcbW90aW9uLWRvbVxcZGlzdFxcZXNcXGdlc3R1cmVzXFx1dGlsc1xcc2V0dXAubWpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHJlc29sdmVFbGVtZW50cyB9IGZyb20gJy4uLy4uL3V0aWxzL3Jlc29sdmUtZWxlbWVudHMubWpzJztcblxuZnVuY3Rpb24gc2V0dXBHZXN0dXJlKGVsZW1lbnRPclNlbGVjdG9yLCBvcHRpb25zKSB7XG4gICAgY29uc3QgZWxlbWVudHMgPSByZXNvbHZlRWxlbWVudHMoZWxlbWVudE9yU2VsZWN0b3IpO1xuICAgIGNvbnN0IGdlc3R1cmVBYm9ydENvbnRyb2xsZXIgPSBuZXcgQWJvcnRDb250cm9sbGVyKCk7XG4gICAgY29uc3QgZXZlbnRPcHRpb25zID0ge1xuICAgICAgICBwYXNzaXZlOiB0cnVlLFxuICAgICAgICAuLi5vcHRpb25zLFxuICAgICAgICBzaWduYWw6IGdlc3R1cmVBYm9ydENvbnRyb2xsZXIuc2lnbmFsLFxuICAgIH07XG4gICAgY29uc3QgY2FuY2VsID0gKCkgPT4gZ2VzdHVyZUFib3J0Q29udHJvbGxlci5hYm9ydCgpO1xuICAgIHJldHVybiBbZWxlbWVudHMsIGV2ZW50T3B0aW9ucywgY2FuY2VsXTtcbn1cblxuZXhwb3J0IHsgc2V0dXBHZXN0dXJlIH07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/motion-dom/dist/es/gestures/utils/setup.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/motion-dom/dist/es/index.mjs":
/*!***************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/index.mjs ***!
  \***************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GroupAnimation: () => (/* reexport safe */ _animation_GroupAnimation_mjs__WEBPACK_IMPORTED_MODULE_0__.GroupAnimation),\n/* harmony export */   GroupAnimationWithThen: () => (/* reexport safe */ _animation_GroupAnimationWithThen_mjs__WEBPACK_IMPORTED_MODULE_1__.GroupAnimationWithThen),\n/* harmony export */   MotionValue: () => (/* reexport safe */ _value_index_mjs__WEBPACK_IMPORTED_MODULE_32__.MotionValue),\n/* harmony export */   NativeAnimation: () => (/* reexport safe */ _animation_NativeAnimation_mjs__WEBPACK_IMPORTED_MODULE_2__.NativeAnimation),\n/* harmony export */   ViewTransitionBuilder: () => (/* reexport safe */ _view_index_mjs__WEBPACK_IMPORTED_MODULE_33__.ViewTransitionBuilder),\n/* harmony export */   activeAnimations: () => (/* reexport safe */ _stats_animation_count_mjs__WEBPACK_IMPORTED_MODULE_25__.activeAnimations),\n/* harmony export */   animateView: () => (/* reexport safe */ _view_index_mjs__WEBPACK_IMPORTED_MODULE_33__.animateView),\n/* harmony export */   attachTimeline: () => (/* reexport safe */ _animation_waapi_utils_attach_timeline_mjs__WEBPACK_IMPORTED_MODULE_12__.attachTimeline),\n/* harmony export */   calcGeneratorDuration: () => (/* reexport safe */ _animation_generators_utils_calc_duration_mjs__WEBPACK_IMPORTED_MODULE_4__.calcGeneratorDuration),\n/* harmony export */   cancelFrame: () => (/* reexport safe */ _frameloop_frame_mjs__WEBPACK_IMPORTED_MODULE_14__.cancelFrame),\n/* harmony export */   cancelMicrotask: () => (/* reexport safe */ _frameloop_microtask_mjs__WEBPACK_IMPORTED_MODULE_16__.cancelMicrotask),\n/* harmony export */   collectMotionValues: () => (/* reexport safe */ _value_index_mjs__WEBPACK_IMPORTED_MODULE_32__.collectMotionValues),\n/* harmony export */   createGeneratorEasing: () => (/* reexport safe */ _animation_generators_utils_create_generator_easing_mjs__WEBPACK_IMPORTED_MODULE_5__.createGeneratorEasing),\n/* harmony export */   createRenderBatcher: () => (/* reexport safe */ _frameloop_batcher_mjs__WEBPACK_IMPORTED_MODULE_15__.createRenderBatcher),\n/* harmony export */   cubicBezierAsString: () => (/* reexport safe */ _animation_waapi_easing_cubic_bezier_mjs__WEBPACK_IMPORTED_MODULE_7__.cubicBezierAsString),\n/* harmony export */   frame: () => (/* reexport safe */ _frameloop_frame_mjs__WEBPACK_IMPORTED_MODULE_14__.frame),\n/* harmony export */   frameData: () => (/* reexport safe */ _frameloop_frame_mjs__WEBPACK_IMPORTED_MODULE_14__.frameData),\n/* harmony export */   frameSteps: () => (/* reexport safe */ _frameloop_frame_mjs__WEBPACK_IMPORTED_MODULE_14__.frameSteps),\n/* harmony export */   generateLinearEasing: () => (/* reexport safe */ _animation_waapi_utils_linear_mjs__WEBPACK_IMPORTED_MODULE_13__.generateLinearEasing),\n/* harmony export */   getValueTransition: () => (/* reexport safe */ _animation_utils_get_value_transition_mjs__WEBPACK_IMPORTED_MODULE_3__.getValueTransition),\n/* harmony export */   hover: () => (/* reexport safe */ _gestures_hover_mjs__WEBPACK_IMPORTED_MODULE_20__.hover),\n/* harmony export */   isBezierDefinition: () => (/* reexport safe */ _utils_is_bezier_definition_mjs__WEBPACK_IMPORTED_MODULE_27__.isBezierDefinition),\n/* harmony export */   isDragActive: () => (/* reexport safe */ _gestures_drag_state_is_active_mjs__WEBPACK_IMPORTED_MODULE_18__.isDragActive),\n/* harmony export */   isDragging: () => (/* reexport safe */ _gestures_drag_state_is_active_mjs__WEBPACK_IMPORTED_MODULE_18__.isDragging),\n/* harmony export */   isGenerator: () => (/* reexport safe */ _animation_generators_utils_is_generator_mjs__WEBPACK_IMPORTED_MODULE_6__.isGenerator),\n/* harmony export */   isNodeOrChild: () => (/* reexport safe */ _gestures_utils_is_node_or_child_mjs__WEBPACK_IMPORTED_MODULE_22__.isNodeOrChild),\n/* harmony export */   isPrimaryPointer: () => (/* reexport safe */ _gestures_utils_is_primary_pointer_mjs__WEBPACK_IMPORTED_MODULE_23__.isPrimaryPointer),\n/* harmony export */   isWaapiSupportedEasing: () => (/* reexport safe */ _animation_waapi_easing_is_supported_mjs__WEBPACK_IMPORTED_MODULE_8__.isWaapiSupportedEasing),\n/* harmony export */   mapEasingToNativeEasing: () => (/* reexport safe */ _animation_waapi_easing_map_easing_mjs__WEBPACK_IMPORTED_MODULE_9__.mapEasingToNativeEasing),\n/* harmony export */   maxGeneratorDuration: () => (/* reexport safe */ _animation_generators_utils_calc_duration_mjs__WEBPACK_IMPORTED_MODULE_4__.maxGeneratorDuration),\n/* harmony export */   microtask: () => (/* reexport safe */ _frameloop_microtask_mjs__WEBPACK_IMPORTED_MODULE_16__.microtask),\n/* harmony export */   motionValue: () => (/* reexport safe */ _value_index_mjs__WEBPACK_IMPORTED_MODULE_32__.motionValue),\n/* harmony export */   press: () => (/* reexport safe */ _gestures_press_index_mjs__WEBPACK_IMPORTED_MODULE_21__.press),\n/* harmony export */   recordStats: () => (/* reexport safe */ _stats_index_mjs__WEBPACK_IMPORTED_MODULE_24__.recordStats),\n/* harmony export */   resolveElements: () => (/* reexport safe */ _utils_resolve_elements_mjs__WEBPACK_IMPORTED_MODULE_28__.resolveElements),\n/* harmony export */   setDragLock: () => (/* reexport safe */ _gestures_drag_state_set_active_mjs__WEBPACK_IMPORTED_MODULE_19__.setDragLock),\n/* harmony export */   startWaapiAnimation: () => (/* reexport safe */ _animation_waapi_start_waapi_animation_mjs__WEBPACK_IMPORTED_MODULE_11__.startWaapiAnimation),\n/* harmony export */   statsBuffer: () => (/* reexport safe */ _stats_buffer_mjs__WEBPACK_IMPORTED_MODULE_26__.statsBuffer),\n/* harmony export */   supportedWaapiEasing: () => (/* reexport safe */ _animation_waapi_easing_supported_mjs__WEBPACK_IMPORTED_MODULE_10__.supportedWaapiEasing),\n/* harmony export */   supportsFlags: () => (/* reexport safe */ _utils_supports_flags_mjs__WEBPACK_IMPORTED_MODULE_29__.supportsFlags),\n/* harmony export */   supportsLinearEasing: () => (/* reexport safe */ _utils_supports_linear_easing_mjs__WEBPACK_IMPORTED_MODULE_30__.supportsLinearEasing),\n/* harmony export */   supportsScrollTimeline: () => (/* reexport safe */ _utils_supports_scroll_timeline_mjs__WEBPACK_IMPORTED_MODULE_31__.supportsScrollTimeline),\n/* harmony export */   time: () => (/* reexport safe */ _frameloop_sync_time_mjs__WEBPACK_IMPORTED_MODULE_17__.time)\n/* harmony export */ });\n/* harmony import */ var _animation_GroupAnimation_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./animation/GroupAnimation.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/animation/GroupAnimation.mjs\");\n/* harmony import */ var _animation_GroupAnimationWithThen_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./animation/GroupAnimationWithThen.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/animation/GroupAnimationWithThen.mjs\");\n/* harmony import */ var _animation_NativeAnimation_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./animation/NativeAnimation.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/animation/NativeAnimation.mjs\");\n/* harmony import */ var _animation_utils_get_value_transition_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./animation/utils/get-value-transition.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/animation/utils/get-value-transition.mjs\");\n/* harmony import */ var _animation_generators_utils_calc_duration_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./animation/generators/utils/calc-duration.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/animation/generators/utils/calc-duration.mjs\");\n/* harmony import */ var _animation_generators_utils_create_generator_easing_mjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./animation/generators/utils/create-generator-easing.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/animation/generators/utils/create-generator-easing.mjs\");\n/* harmony import */ var _animation_generators_utils_is_generator_mjs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./animation/generators/utils/is-generator.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/animation/generators/utils/is-generator.mjs\");\n/* harmony import */ var _animation_waapi_easing_cubic_bezier_mjs__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./animation/waapi/easing/cubic-bezier.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/animation/waapi/easing/cubic-bezier.mjs\");\n/* harmony import */ var _animation_waapi_easing_is_supported_mjs__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./animation/waapi/easing/is-supported.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/animation/waapi/easing/is-supported.mjs\");\n/* harmony import */ var _animation_waapi_easing_map_easing_mjs__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./animation/waapi/easing/map-easing.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/animation/waapi/easing/map-easing.mjs\");\n/* harmony import */ var _animation_waapi_easing_supported_mjs__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./animation/waapi/easing/supported.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/animation/waapi/easing/supported.mjs\");\n/* harmony import */ var _animation_waapi_start_waapi_animation_mjs__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./animation/waapi/start-waapi-animation.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/animation/waapi/start-waapi-animation.mjs\");\n/* harmony import */ var _animation_waapi_utils_attach_timeline_mjs__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./animation/waapi/utils/attach-timeline.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/animation/waapi/utils/attach-timeline.mjs\");\n/* harmony import */ var _animation_waapi_utils_linear_mjs__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./animation/waapi/utils/linear.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/animation/waapi/utils/linear.mjs\");\n/* harmony import */ var _frameloop_frame_mjs__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./frameloop/frame.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/frameloop/frame.mjs\");\n/* harmony import */ var _frameloop_batcher_mjs__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ./frameloop/batcher.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/frameloop/batcher.mjs\");\n/* harmony import */ var _frameloop_microtask_mjs__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ./frameloop/microtask.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/frameloop/microtask.mjs\");\n/* harmony import */ var _frameloop_sync_time_mjs__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! ./frameloop/sync-time.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/frameloop/sync-time.mjs\");\n/* harmony import */ var _gestures_drag_state_is_active_mjs__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! ./gestures/drag/state/is-active.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/gestures/drag/state/is-active.mjs\");\n/* harmony import */ var _gestures_drag_state_set_active_mjs__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! ./gestures/drag/state/set-active.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/gestures/drag/state/set-active.mjs\");\n/* harmony import */ var _gestures_hover_mjs__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! ./gestures/hover.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/gestures/hover.mjs\");\n/* harmony import */ var _gestures_press_index_mjs__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! ./gestures/press/index.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/gestures/press/index.mjs\");\n/* harmony import */ var _gestures_utils_is_node_or_child_mjs__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! ./gestures/utils/is-node-or-child.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/gestures/utils/is-node-or-child.mjs\");\n/* harmony import */ var _gestures_utils_is_primary_pointer_mjs__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! ./gestures/utils/is-primary-pointer.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/gestures/utils/is-primary-pointer.mjs\");\n/* harmony import */ var _stats_index_mjs__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! ./stats/index.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/stats/index.mjs\");\n/* harmony import */ var _stats_animation_count_mjs__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! ./stats/animation-count.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/stats/animation-count.mjs\");\n/* harmony import */ var _stats_buffer_mjs__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! ./stats/buffer.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/stats/buffer.mjs\");\n/* harmony import */ var _utils_is_bezier_definition_mjs__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! ./utils/is-bezier-definition.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/utils/is-bezier-definition.mjs\");\n/* harmony import */ var _utils_resolve_elements_mjs__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! ./utils/resolve-elements.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/utils/resolve-elements.mjs\");\n/* harmony import */ var _utils_supports_flags_mjs__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! ./utils/supports/flags.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/utils/supports/flags.mjs\");\n/* harmony import */ var _utils_supports_linear_easing_mjs__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! ./utils/supports/linear-easing.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/utils/supports/linear-easing.mjs\");\n/* harmony import */ var _utils_supports_scroll_timeline_mjs__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! ./utils/supports/scroll-timeline.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/utils/supports/scroll-timeline.mjs\");\n/* harmony import */ var _value_index_mjs__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! ./value/index.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/value/index.mjs\");\n/* harmony import */ var _view_index_mjs__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! ./view/index.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/view/index.mjs\");\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/motion-dom/dist/es/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/motion-dom/dist/es/render/dom/style.mjs":
/*!**************************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/render/dom/style.mjs ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   style: () => (/* binding */ style)\n/* harmony export */ });\nconst isCSSVar = (name) => name.startsWith(\"--\");\nconst style = {\n    set: (element, name, value) => {\n        isCSSVar(name)\n            ? element.style.setProperty(name, value)\n            : (element.style[name] = value);\n    },\n    get: (element, name) => {\n        return isCSSVar(name)\n            ? element.style.getPropertyValue(name)\n            : element.style[name];\n    },\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbW90aW9uLWRvbS9kaXN0L2VzL3JlbmRlci9kb20vc3R5bGUubWpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7O0FBRWlCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHNoaXZhXFxPbmVEcml2ZVxcRGVza3RvcFxcbWtcXENhc2htaW5kZXItLS1Nb25leS1NYW5hZ2VtZW50LUFwcFxcbm9kZV9tb2R1bGVzXFxtb3Rpb24tZG9tXFxkaXN0XFxlc1xccmVuZGVyXFxkb21cXHN0eWxlLm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCBpc0NTU1ZhciA9IChuYW1lKSA9PiBuYW1lLnN0YXJ0c1dpdGgoXCItLVwiKTtcbmNvbnN0IHN0eWxlID0ge1xuICAgIHNldDogKGVsZW1lbnQsIG5hbWUsIHZhbHVlKSA9PiB7XG4gICAgICAgIGlzQ1NTVmFyKG5hbWUpXG4gICAgICAgICAgICA/IGVsZW1lbnQuc3R5bGUuc2V0UHJvcGVydHkobmFtZSwgdmFsdWUpXG4gICAgICAgICAgICA6IChlbGVtZW50LnN0eWxlW25hbWVdID0gdmFsdWUpO1xuICAgIH0sXG4gICAgZ2V0OiAoZWxlbWVudCwgbmFtZSkgPT4ge1xuICAgICAgICByZXR1cm4gaXNDU1NWYXIobmFtZSlcbiAgICAgICAgICAgID8gZWxlbWVudC5zdHlsZS5nZXRQcm9wZXJ0eVZhbHVlKG5hbWUpXG4gICAgICAgICAgICA6IGVsZW1lbnQuc3R5bGVbbmFtZV07XG4gICAgfSxcbn07XG5cbmV4cG9ydCB7IHN0eWxlIH07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/motion-dom/dist/es/render/dom/style.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/motion-dom/dist/es/stats/animation-count.mjs":
/*!*******************************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/stats/animation-count.mjs ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   activeAnimations: () => (/* binding */ activeAnimations)\n/* harmony export */ });\nconst activeAnimations = {\n    layout: 0,\n    mainThread: 0,\n    waapi: 0,\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbW90aW9uLWRvbS9kaXN0L2VzL3N0YXRzL2FuaW1hdGlvbi1jb3VudC5tanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRTRCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHNoaXZhXFxPbmVEcml2ZVxcRGVza3RvcFxcbWtcXENhc2htaW5kZXItLS1Nb25leS1NYW5hZ2VtZW50LUFwcFxcbm9kZV9tb2R1bGVzXFxtb3Rpb24tZG9tXFxkaXN0XFxlc1xcc3RhdHNcXGFuaW1hdGlvbi1jb3VudC5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiY29uc3QgYWN0aXZlQW5pbWF0aW9ucyA9IHtcbiAgICBsYXlvdXQ6IDAsXG4gICAgbWFpblRocmVhZDogMCxcbiAgICB3YWFwaTogMCxcbn07XG5cbmV4cG9ydCB7IGFjdGl2ZUFuaW1hdGlvbnMgfTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/motion-dom/dist/es/stats/animation-count.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/motion-dom/dist/es/stats/buffer.mjs":
/*!**********************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/stats/buffer.mjs ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   statsBuffer: () => (/* binding */ statsBuffer)\n/* harmony export */ });\nconst statsBuffer = {\n    value: null,\n    addProjectionMetrics: null,\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbW90aW9uLWRvbS9kaXN0L2VzL3N0YXRzL2J1ZmZlci5tanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBOztBQUV1QiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxzaGl2YVxcT25lRHJpdmVcXERlc2t0b3BcXG1rXFxDYXNobWluZGVyLS0tTW9uZXktTWFuYWdlbWVudC1BcHBcXG5vZGVfbW9kdWxlc1xcbW90aW9uLWRvbVxcZGlzdFxcZXNcXHN0YXRzXFxidWZmZXIubWpzIl0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IHN0YXRzQnVmZmVyID0ge1xuICAgIHZhbHVlOiBudWxsLFxuICAgIGFkZFByb2plY3Rpb25NZXRyaWNzOiBudWxsLFxufTtcblxuZXhwb3J0IHsgc3RhdHNCdWZmZXIgfTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/motion-dom/dist/es/stats/buffer.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/motion-dom/dist/es/stats/index.mjs":
/*!*********************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/stats/index.mjs ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   recordStats: () => (/* binding */ recordStats)\n/* harmony export */ });\n/* harmony import */ var _frameloop_frame_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../frameloop/frame.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/frameloop/frame.mjs\");\n/* harmony import */ var _animation_count_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./animation-count.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/stats/animation-count.mjs\");\n/* harmony import */ var _buffer_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./buffer.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/stats/buffer.mjs\");\n\n\n\n\nfunction record() {\n    const { value } = _buffer_mjs__WEBPACK_IMPORTED_MODULE_2__.statsBuffer;\n    if (value === null) {\n        (0,_frameloop_frame_mjs__WEBPACK_IMPORTED_MODULE_0__.cancelFrame)(record);\n        return;\n    }\n    value.frameloop.rate.push(_frameloop_frame_mjs__WEBPACK_IMPORTED_MODULE_0__.frameData.delta);\n    value.animations.mainThread.push(_animation_count_mjs__WEBPACK_IMPORTED_MODULE_1__.activeAnimations.mainThread);\n    value.animations.waapi.push(_animation_count_mjs__WEBPACK_IMPORTED_MODULE_1__.activeAnimations.waapi);\n    value.animations.layout.push(_animation_count_mjs__WEBPACK_IMPORTED_MODULE_1__.activeAnimations.layout);\n}\nfunction mean(values) {\n    return values.reduce((acc, value) => acc + value, 0) / values.length;\n}\nfunction summarise(values, calcAverage = mean) {\n    if (values.length === 0) {\n        return {\n            min: 0,\n            max: 0,\n            avg: 0,\n        };\n    }\n    return {\n        min: Math.min(...values),\n        max: Math.max(...values),\n        avg: calcAverage(values),\n    };\n}\nconst msToFps = (ms) => Math.round(1000 / ms);\nfunction clearStatsBuffer() {\n    _buffer_mjs__WEBPACK_IMPORTED_MODULE_2__.statsBuffer.value = null;\n    _buffer_mjs__WEBPACK_IMPORTED_MODULE_2__.statsBuffer.addProjectionMetrics = null;\n}\nfunction reportStats() {\n    const { value } = _buffer_mjs__WEBPACK_IMPORTED_MODULE_2__.statsBuffer;\n    if (!value) {\n        throw new Error(\"Stats are not being measured\");\n    }\n    clearStatsBuffer();\n    (0,_frameloop_frame_mjs__WEBPACK_IMPORTED_MODULE_0__.cancelFrame)(record);\n    const summary = {\n        frameloop: {\n            rate: summarise(value.frameloop.rate),\n            read: summarise(value.frameloop.read),\n            resolveKeyframes: summarise(value.frameloop.resolveKeyframes),\n            update: summarise(value.frameloop.update),\n            preRender: summarise(value.frameloop.preRender),\n            render: summarise(value.frameloop.render),\n            postRender: summarise(value.frameloop.postRender),\n        },\n        animations: {\n            mainThread: summarise(value.animations.mainThread),\n            waapi: summarise(value.animations.waapi),\n            layout: summarise(value.animations.layout),\n        },\n        layoutProjection: {\n            nodes: summarise(value.layoutProjection.nodes),\n            calculatedTargetDeltas: summarise(value.layoutProjection.calculatedTargetDeltas),\n            calculatedProjections: summarise(value.layoutProjection.calculatedProjections),\n        },\n    };\n    /**\n     * Convert the rate to FPS\n     */\n    const { rate } = summary.frameloop;\n    rate.min = msToFps(rate.min);\n    rate.max = msToFps(rate.max);\n    rate.avg = msToFps(rate.avg);\n    [rate.min, rate.max] = [rate.max, rate.min];\n    return summary;\n}\nfunction recordStats() {\n    if (_buffer_mjs__WEBPACK_IMPORTED_MODULE_2__.statsBuffer.value) {\n        clearStatsBuffer();\n        throw new Error(\"Stats are already being measured\");\n    }\n    const newStatsBuffer = _buffer_mjs__WEBPACK_IMPORTED_MODULE_2__.statsBuffer;\n    newStatsBuffer.value = {\n        frameloop: {\n            rate: [],\n            read: [],\n            resolveKeyframes: [],\n            update: [],\n            preRender: [],\n            render: [],\n            postRender: [],\n        },\n        animations: {\n            mainThread: [],\n            waapi: [],\n            layout: [],\n        },\n        layoutProjection: {\n            nodes: [],\n            calculatedTargetDeltas: [],\n            calculatedProjections: [],\n        },\n    };\n    newStatsBuffer.addProjectionMetrics = (metrics) => {\n        const { layoutProjection } = newStatsBuffer.value;\n        layoutProjection.nodes.push(metrics.nodes);\n        layoutProjection.calculatedTargetDeltas.push(metrics.calculatedTargetDeltas);\n        layoutProjection.calculatedProjections.push(metrics.calculatedProjections);\n    };\n    _frameloop_frame_mjs__WEBPACK_IMPORTED_MODULE_0__.frame.postRender(record, true);\n    return reportStats;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/motion-dom/dist/es/stats/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/motion-dom/dist/es/utils/is-bezier-definition.mjs":
/*!************************************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/utils/is-bezier-definition.mjs ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isBezierDefinition: () => (/* binding */ isBezierDefinition)\n/* harmony export */ });\nconst isBezierDefinition = (easing) => Array.isArray(easing) && typeof easing[0] === \"number\";\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbW90aW9uLWRvbS9kaXN0L2VzL3V0aWxzL2lzLWJlemllci1kZWZpbml0aW9uLm1qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7O0FBRThCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHNoaXZhXFxPbmVEcml2ZVxcRGVza3RvcFxcbWtcXENhc2htaW5kZXItLS1Nb25leS1NYW5hZ2VtZW50LUFwcFxcbm9kZV9tb2R1bGVzXFxtb3Rpb24tZG9tXFxkaXN0XFxlc1xcdXRpbHNcXGlzLWJlemllci1kZWZpbml0aW9uLm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCBpc0JlemllckRlZmluaXRpb24gPSAoZWFzaW5nKSA9PiBBcnJheS5pc0FycmF5KGVhc2luZykgJiYgdHlwZW9mIGVhc2luZ1swXSA9PT0gXCJudW1iZXJcIjtcblxuZXhwb3J0IHsgaXNCZXppZXJEZWZpbml0aW9uIH07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/motion-dom/dist/es/utils/is-bezier-definition.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/motion-dom/dist/es/utils/resolve-elements.mjs":
/*!********************************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/utils/resolve-elements.mjs ***!
  \********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   resolveElements: () => (/* binding */ resolveElements)\n/* harmony export */ });\nfunction resolveElements(elementOrSelector, scope, selectorCache) {\n    if (elementOrSelector instanceof EventTarget) {\n        return [elementOrSelector];\n    }\n    else if (typeof elementOrSelector === \"string\") {\n        let root = document;\n        if (scope) {\n            root = scope.current;\n        }\n        const elements = selectorCache?.[elementOrSelector] ??\n            root.querySelectorAll(elementOrSelector);\n        return elements ? Array.from(elements) : [];\n    }\n    return Array.from(elementOrSelector);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbW90aW9uLWRvbS9kaXN0L2VzL3V0aWxzL3Jlc29sdmUtZWxlbWVudHMubWpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRTJCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHNoaXZhXFxPbmVEcml2ZVxcRGVza3RvcFxcbWtcXENhc2htaW5kZXItLS1Nb25leS1NYW5hZ2VtZW50LUFwcFxcbm9kZV9tb2R1bGVzXFxtb3Rpb24tZG9tXFxkaXN0XFxlc1xcdXRpbHNcXHJlc29sdmUtZWxlbWVudHMubWpzIl0sInNvdXJjZXNDb250ZW50IjpbImZ1bmN0aW9uIHJlc29sdmVFbGVtZW50cyhlbGVtZW50T3JTZWxlY3Rvciwgc2NvcGUsIHNlbGVjdG9yQ2FjaGUpIHtcbiAgICBpZiAoZWxlbWVudE9yU2VsZWN0b3IgaW5zdGFuY2VvZiBFdmVudFRhcmdldCkge1xuICAgICAgICByZXR1cm4gW2VsZW1lbnRPclNlbGVjdG9yXTtcbiAgICB9XG4gICAgZWxzZSBpZiAodHlwZW9mIGVsZW1lbnRPclNlbGVjdG9yID09PSBcInN0cmluZ1wiKSB7XG4gICAgICAgIGxldCByb290ID0gZG9jdW1lbnQ7XG4gICAgICAgIGlmIChzY29wZSkge1xuICAgICAgICAgICAgcm9vdCA9IHNjb3BlLmN1cnJlbnQ7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgZWxlbWVudHMgPSBzZWxlY3RvckNhY2hlPy5bZWxlbWVudE9yU2VsZWN0b3JdID8/XG4gICAgICAgICAgICByb290LnF1ZXJ5U2VsZWN0b3JBbGwoZWxlbWVudE9yU2VsZWN0b3IpO1xuICAgICAgICByZXR1cm4gZWxlbWVudHMgPyBBcnJheS5mcm9tKGVsZW1lbnRzKSA6IFtdO1xuICAgIH1cbiAgICByZXR1cm4gQXJyYXkuZnJvbShlbGVtZW50T3JTZWxlY3Rvcik7XG59XG5cbmV4cG9ydCB7IHJlc29sdmVFbGVtZW50cyB9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/motion-dom/dist/es/utils/resolve-elements.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/motion-dom/dist/es/utils/supports/flags.mjs":
/*!******************************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/utils/supports/flags.mjs ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   supportsFlags: () => (/* binding */ supportsFlags)\n/* harmony export */ });\n/**\n * Add the ability for test suites to manually set support flags\n * to better test more environments.\n */\nconst supportsFlags = {};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbW90aW9uLWRvbS9kaXN0L2VzL3V0aWxzL3N1cHBvcnRzL2ZsYWdzLm1qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFeUIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcc2hpdmFcXE9uZURyaXZlXFxEZXNrdG9wXFxta1xcQ2FzaG1pbmRlci0tLU1vbmV5LU1hbmFnZW1lbnQtQXBwXFxub2RlX21vZHVsZXNcXG1vdGlvbi1kb21cXGRpc3RcXGVzXFx1dGlsc1xcc3VwcG9ydHNcXGZsYWdzLm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEFkZCB0aGUgYWJpbGl0eSBmb3IgdGVzdCBzdWl0ZXMgdG8gbWFudWFsbHkgc2V0IHN1cHBvcnQgZmxhZ3NcbiAqIHRvIGJldHRlciB0ZXN0IG1vcmUgZW52aXJvbm1lbnRzLlxuICovXG5jb25zdCBzdXBwb3J0c0ZsYWdzID0ge307XG5cbmV4cG9ydCB7IHN1cHBvcnRzRmxhZ3MgfTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/motion-dom/dist/es/utils/supports/flags.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/motion-dom/dist/es/utils/supports/linear-easing.mjs":
/*!**************************************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/utils/supports/linear-easing.mjs ***!
  \**************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   supportsLinearEasing: () => (/* binding */ supportsLinearEasing)\n/* harmony export */ });\n/* harmony import */ var _memo_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./memo.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/utils/supports/memo.mjs\");\n\n\nconst supportsLinearEasing = /*@__PURE__*/ (0,_memo_mjs__WEBPACK_IMPORTED_MODULE_0__.memoSupports)(() => {\n    try {\n        document\n            .createElement(\"div\")\n            .animate({ opacity: 0 }, { easing: \"linear(0, 1)\" });\n    }\n    catch (e) {\n        return false;\n    }\n    return true;\n}, \"linearEasing\");\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbW90aW9uLWRvbS9kaXN0L2VzL3V0aWxzL3N1cHBvcnRzL2xpbmVhci1lYXNpbmcubWpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQTBDOztBQUUxQywyQ0FBMkMsdURBQVk7QUFDdkQ7QUFDQTtBQUNBO0FBQ0EsdUJBQXVCLFlBQVksSUFBSSx3QkFBd0I7QUFDL0Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUM7O0FBRStCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHNoaXZhXFxPbmVEcml2ZVxcRGVza3RvcFxcbWtcXENhc2htaW5kZXItLS1Nb25leS1NYW5hZ2VtZW50LUFwcFxcbm9kZV9tb2R1bGVzXFxtb3Rpb24tZG9tXFxkaXN0XFxlc1xcdXRpbHNcXHN1cHBvcnRzXFxsaW5lYXItZWFzaW5nLm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBtZW1vU3VwcG9ydHMgfSBmcm9tICcuL21lbW8ubWpzJztcblxuY29uc3Qgc3VwcG9ydHNMaW5lYXJFYXNpbmcgPSAvKkBfX1BVUkVfXyovIG1lbW9TdXBwb3J0cygoKSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgICAgZG9jdW1lbnRcbiAgICAgICAgICAgIC5jcmVhdGVFbGVtZW50KFwiZGl2XCIpXG4gICAgICAgICAgICAuYW5pbWF0ZSh7IG9wYWNpdHk6IDAgfSwgeyBlYXNpbmc6IFwibGluZWFyKDAsIDEpXCIgfSk7XG4gICAgfVxuICAgIGNhdGNoIChlKSB7XG4gICAgICAgIHJldHVybiBmYWxzZTtcbiAgICB9XG4gICAgcmV0dXJuIHRydWU7XG59LCBcImxpbmVhckVhc2luZ1wiKTtcblxuZXhwb3J0IHsgc3VwcG9ydHNMaW5lYXJFYXNpbmcgfTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/motion-dom/dist/es/utils/supports/linear-easing.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/motion-dom/dist/es/utils/supports/memo.mjs":
/*!*****************************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/utils/supports/memo.mjs ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   memoSupports: () => (/* binding */ memoSupports)\n/* harmony export */ });\n/* harmony import */ var motion_utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! motion-utils */ \"(ssr)/./node_modules/motion-utils/dist/es/index.mjs\");\n/* harmony import */ var _flags_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./flags.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/utils/supports/flags.mjs\");\n\n\n\nfunction memoSupports(callback, supportsFlag) {\n    const memoized = (0,motion_utils__WEBPACK_IMPORTED_MODULE_0__.memo)(callback);\n    return () => _flags_mjs__WEBPACK_IMPORTED_MODULE_1__.supportsFlags[supportsFlag] ?? memoized();\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbW90aW9uLWRvbS9kaXN0L2VzL3V0aWxzL3N1cHBvcnRzL21lbW8ubWpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFvQztBQUNROztBQUU1QztBQUNBLHFCQUFxQixrREFBSTtBQUN6QixpQkFBaUIscURBQWE7QUFDOUI7O0FBRXdCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHNoaXZhXFxPbmVEcml2ZVxcRGVza3RvcFxcbWtcXENhc2htaW5kZXItLS1Nb25leS1NYW5hZ2VtZW50LUFwcFxcbm9kZV9tb2R1bGVzXFxtb3Rpb24tZG9tXFxkaXN0XFxlc1xcdXRpbHNcXHN1cHBvcnRzXFxtZW1vLm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBtZW1vIH0gZnJvbSAnbW90aW9uLXV0aWxzJztcbmltcG9ydCB7IHN1cHBvcnRzRmxhZ3MgfSBmcm9tICcuL2ZsYWdzLm1qcyc7XG5cbmZ1bmN0aW9uIG1lbW9TdXBwb3J0cyhjYWxsYmFjaywgc3VwcG9ydHNGbGFnKSB7XG4gICAgY29uc3QgbWVtb2l6ZWQgPSBtZW1vKGNhbGxiYWNrKTtcbiAgICByZXR1cm4gKCkgPT4gc3VwcG9ydHNGbGFnc1tzdXBwb3J0c0ZsYWddID8/IG1lbW9pemVkKCk7XG59XG5cbmV4cG9ydCB7IG1lbW9TdXBwb3J0cyB9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/motion-dom/dist/es/utils/supports/memo.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/motion-dom/dist/es/utils/supports/scroll-timeline.mjs":
/*!****************************************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/utils/supports/scroll-timeline.mjs ***!
  \****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   supportsScrollTimeline: () => (/* binding */ supportsScrollTimeline)\n/* harmony export */ });\n/* harmony import */ var motion_utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! motion-utils */ \"(ssr)/./node_modules/motion-utils/dist/es/index.mjs\");\n\n\nconst supportsScrollTimeline = /* @__PURE__ */ (0,motion_utils__WEBPACK_IMPORTED_MODULE_0__.memo)(() => window.ScrollTimeline !== undefined);\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbW90aW9uLWRvbS9kaXN0L2VzL3V0aWxzL3N1cHBvcnRzL3Njcm9sbC10aW1lbGluZS5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBb0M7O0FBRXBDLCtDQUErQyxrREFBSTs7QUFFakIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcc2hpdmFcXE9uZURyaXZlXFxEZXNrdG9wXFxta1xcQ2FzaG1pbmRlci0tLU1vbmV5LU1hbmFnZW1lbnQtQXBwXFxub2RlX21vZHVsZXNcXG1vdGlvbi1kb21cXGRpc3RcXGVzXFx1dGlsc1xcc3VwcG9ydHNcXHNjcm9sbC10aW1lbGluZS5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgbWVtbyB9IGZyb20gJ21vdGlvbi11dGlscyc7XG5cbmNvbnN0IHN1cHBvcnRzU2Nyb2xsVGltZWxpbmUgPSAvKiBAX19QVVJFX18gKi8gbWVtbygoKSA9PiB3aW5kb3cuU2Nyb2xsVGltZWxpbmUgIT09IHVuZGVmaW5lZCk7XG5cbmV4cG9ydCB7IHN1cHBvcnRzU2Nyb2xsVGltZWxpbmUgfTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/motion-dom/dist/es/utils/supports/scroll-timeline.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/motion-dom/dist/es/value/index.mjs":
/*!*********************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/value/index.mjs ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MotionValue: () => (/* binding */ MotionValue),\n/* harmony export */   collectMotionValues: () => (/* binding */ collectMotionValues),\n/* harmony export */   motionValue: () => (/* binding */ motionValue)\n/* harmony export */ });\n/* harmony import */ var motion_utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! motion-utils */ \"(ssr)/./node_modules/motion-utils/dist/es/index.mjs\");\n/* harmony import */ var _frameloop_frame_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../frameloop/frame.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/frameloop/frame.mjs\");\n/* harmony import */ var _frameloop_sync_time_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../frameloop/sync-time.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/frameloop/sync-time.mjs\");\n\n\n\n\n/**\n * Maximum time between the value of two frames, beyond which we\n * assume the velocity has since been 0.\n */\nconst MAX_VELOCITY_DELTA = 30;\nconst isFloat = (value) => {\n    return !isNaN(parseFloat(value));\n};\nconst collectMotionValues = {\n    current: undefined,\n};\n/**\n * `MotionValue` is used to track the state and velocity of motion values.\n *\n * @public\n */\nclass MotionValue {\n    /**\n     * @param init - The initiating value\n     * @param config - Optional configuration options\n     *\n     * -  `transformer`: A function to transform incoming values with.\n     */\n    constructor(init, options = {}) {\n        /**\n         * This will be replaced by the build step with the latest version number.\n         * When MotionValues are provided to motion components, warn if versions are mixed.\n         */\n        this.version = \"12.6.3\";\n        /**\n         * Tracks whether this value can output a velocity. Currently this is only true\n         * if the value is numerical, but we might be able to widen the scope here and support\n         * other value types.\n         *\n         * @internal\n         */\n        this.canTrackVelocity = null;\n        /**\n         * An object containing a SubscriptionManager for each active event.\n         */\n        this.events = {};\n        this.updateAndNotify = (v, render = true) => {\n            const currentTime = _frameloop_sync_time_mjs__WEBPACK_IMPORTED_MODULE_2__.time.now();\n            /**\n             * If we're updating the value during another frame or eventloop\n             * than the previous frame, then the we set the previous frame value\n             * to current.\n             */\n            if (this.updatedAt !== currentTime) {\n                this.setPrevFrameValue();\n            }\n            this.prev = this.current;\n            this.setCurrent(v);\n            // Update update subscribers\n            if (this.current !== this.prev && this.events.change) {\n                this.events.change.notify(this.current);\n            }\n            // Update render subscribers\n            if (render && this.events.renderRequest) {\n                this.events.renderRequest.notify(this.current);\n            }\n        };\n        this.hasAnimated = false;\n        this.setCurrent(init);\n        this.owner = options.owner;\n    }\n    setCurrent(current) {\n        this.current = current;\n        this.updatedAt = _frameloop_sync_time_mjs__WEBPACK_IMPORTED_MODULE_2__.time.now();\n        if (this.canTrackVelocity === null && current !== undefined) {\n            this.canTrackVelocity = isFloat(this.current);\n        }\n    }\n    setPrevFrameValue(prevFrameValue = this.current) {\n        this.prevFrameValue = prevFrameValue;\n        this.prevUpdatedAt = this.updatedAt;\n    }\n    /**\n     * Adds a function that will be notified when the `MotionValue` is updated.\n     *\n     * It returns a function that, when called, will cancel the subscription.\n     *\n     * When calling `onChange` inside a React component, it should be wrapped with the\n     * `useEffect` hook. As it returns an unsubscribe function, this should be returned\n     * from the `useEffect` function to ensure you don't add duplicate subscribers..\n     *\n     * ```jsx\n     * export const MyComponent = () => {\n     *   const x = useMotionValue(0)\n     *   const y = useMotionValue(0)\n     *   const opacity = useMotionValue(1)\n     *\n     *   useEffect(() => {\n     *     function updateOpacity() {\n     *       const maxXY = Math.max(x.get(), y.get())\n     *       const newOpacity = transform(maxXY, [0, 100], [1, 0])\n     *       opacity.set(newOpacity)\n     *     }\n     *\n     *     const unsubscribeX = x.on(\"change\", updateOpacity)\n     *     const unsubscribeY = y.on(\"change\", updateOpacity)\n     *\n     *     return () => {\n     *       unsubscribeX()\n     *       unsubscribeY()\n     *     }\n     *   }, [])\n     *\n     *   return <motion.div style={{ x }} />\n     * }\n     * ```\n     *\n     * @param subscriber - A function that receives the latest value.\n     * @returns A function that, when called, will cancel this subscription.\n     *\n     * @deprecated\n     */\n    onChange(subscription) {\n        if (true) {\n            (0,motion_utils__WEBPACK_IMPORTED_MODULE_0__.warnOnce)(false, `value.onChange(callback) is deprecated. Switch to value.on(\"change\", callback).`);\n        }\n        return this.on(\"change\", subscription);\n    }\n    on(eventName, callback) {\n        if (!this.events[eventName]) {\n            this.events[eventName] = new motion_utils__WEBPACK_IMPORTED_MODULE_0__.SubscriptionManager();\n        }\n        const unsubscribe = this.events[eventName].add(callback);\n        if (eventName === \"change\") {\n            return () => {\n                unsubscribe();\n                /**\n                 * If we have no more change listeners by the start\n                 * of the next frame, stop active animations.\n                 */\n                _frameloop_frame_mjs__WEBPACK_IMPORTED_MODULE_1__.frame.read(() => {\n                    if (!this.events.change.getSize()) {\n                        this.stop();\n                    }\n                });\n            };\n        }\n        return unsubscribe;\n    }\n    clearListeners() {\n        for (const eventManagers in this.events) {\n            this.events[eventManagers].clear();\n        }\n    }\n    /**\n     * Attaches a passive effect to the `MotionValue`.\n     */\n    attach(passiveEffect, stopPassiveEffect) {\n        this.passiveEffect = passiveEffect;\n        this.stopPassiveEffect = stopPassiveEffect;\n    }\n    /**\n     * Sets the state of the `MotionValue`.\n     *\n     * @remarks\n     *\n     * ```jsx\n     * const x = useMotionValue(0)\n     * x.set(10)\n     * ```\n     *\n     * @param latest - Latest value to set.\n     * @param render - Whether to notify render subscribers. Defaults to `true`\n     *\n     * @public\n     */\n    set(v, render = true) {\n        if (!render || !this.passiveEffect) {\n            this.updateAndNotify(v, render);\n        }\n        else {\n            this.passiveEffect(v, this.updateAndNotify);\n        }\n    }\n    setWithVelocity(prev, current, delta) {\n        this.set(current);\n        this.prev = undefined;\n        this.prevFrameValue = prev;\n        this.prevUpdatedAt = this.updatedAt - delta;\n    }\n    /**\n     * Set the state of the `MotionValue`, stopping any active animations,\n     * effects, and resets velocity to `0`.\n     */\n    jump(v, endAnimation = true) {\n        this.updateAndNotify(v);\n        this.prev = v;\n        this.prevUpdatedAt = this.prevFrameValue = undefined;\n        endAnimation && this.stop();\n        if (this.stopPassiveEffect)\n            this.stopPassiveEffect();\n    }\n    /**\n     * Returns the latest state of `MotionValue`\n     *\n     * @returns - The latest state of `MotionValue`\n     *\n     * @public\n     */\n    get() {\n        if (collectMotionValues.current) {\n            collectMotionValues.current.push(this);\n        }\n        return this.current;\n    }\n    /**\n     * @public\n     */\n    getPrevious() {\n        return this.prev;\n    }\n    /**\n     * Returns the latest velocity of `MotionValue`\n     *\n     * @returns - The latest velocity of `MotionValue`. Returns `0` if the state is non-numerical.\n     *\n     * @public\n     */\n    getVelocity() {\n        const currentTime = _frameloop_sync_time_mjs__WEBPACK_IMPORTED_MODULE_2__.time.now();\n        if (!this.canTrackVelocity ||\n            this.prevFrameValue === undefined ||\n            currentTime - this.updatedAt > MAX_VELOCITY_DELTA) {\n            return 0;\n        }\n        const delta = Math.min(this.updatedAt - this.prevUpdatedAt, MAX_VELOCITY_DELTA);\n        // Casts because of parseFloat's poor typing\n        return (0,motion_utils__WEBPACK_IMPORTED_MODULE_0__.velocityPerSecond)(parseFloat(this.current) -\n            parseFloat(this.prevFrameValue), delta);\n    }\n    /**\n     * Registers a new animation to control this `MotionValue`. Only one\n     * animation can drive a `MotionValue` at one time.\n     *\n     * ```jsx\n     * value.start()\n     * ```\n     *\n     * @param animation - A function that starts the provided animation\n     */\n    start(startAnimation) {\n        this.stop();\n        return new Promise((resolve) => {\n            this.hasAnimated = true;\n            this.animation = startAnimation(resolve);\n            if (this.events.animationStart) {\n                this.events.animationStart.notify();\n            }\n        }).then(() => {\n            if (this.events.animationComplete) {\n                this.events.animationComplete.notify();\n            }\n            this.clearAnimation();\n        });\n    }\n    /**\n     * Stop the currently active animation.\n     *\n     * @public\n     */\n    stop() {\n        if (this.animation) {\n            this.animation.stop();\n            if (this.events.animationCancel) {\n                this.events.animationCancel.notify();\n            }\n        }\n        this.clearAnimation();\n    }\n    /**\n     * Returns `true` if this value is currently animating.\n     *\n     * @public\n     */\n    isAnimating() {\n        return !!this.animation;\n    }\n    clearAnimation() {\n        delete this.animation;\n    }\n    /**\n     * Destroy and clean up subscribers to this `MotionValue`.\n     *\n     * The `MotionValue` hooks like `useMotionValue` and `useTransform` automatically\n     * handle the lifecycle of the returned `MotionValue`, so this method is only necessary if you've manually\n     * created a `MotionValue` via the `motionValue` function.\n     *\n     * @public\n     */\n    destroy() {\n        this.clearListeners();\n        this.stop();\n        if (this.stopPassiveEffect) {\n            this.stopPassiveEffect();\n        }\n    }\n}\nfunction motionValue(init, options) {\n    return new MotionValue(init, options);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/motion-dom/dist/es/value/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/motion-dom/dist/es/view/index.mjs":
/*!********************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/view/index.mjs ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ViewTransitionBuilder: () => (/* binding */ ViewTransitionBuilder),\n/* harmony export */   animateView: () => (/* binding */ animateView)\n/* harmony export */ });\n/* harmony import */ var motion_utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! motion-utils */ \"(ssr)/./node_modules/motion-utils/dist/es/index.mjs\");\n/* harmony import */ var _queue_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./queue.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/view/queue.mjs\");\n\n\n\nclass ViewTransitionBuilder {\n    constructor(update, options = {}) {\n        this.currentTarget = \"root\";\n        this.targets = new Map();\n        this.notifyReady = motion_utils__WEBPACK_IMPORTED_MODULE_0__.noop;\n        this.readyPromise = new Promise((resolve) => {\n            this.notifyReady = resolve;\n        });\n        this.update = update;\n        this.options = {\n            interrupt: \"wait\",\n            ...options,\n        };\n        (0,_queue_mjs__WEBPACK_IMPORTED_MODULE_1__.addToQueue)(this);\n    }\n    get(selector) {\n        this.currentTarget = selector;\n        return this;\n    }\n    layout(keyframes, options) {\n        this.updateTarget(\"layout\", keyframes, options);\n        return this;\n    }\n    new(keyframes, options) {\n        this.updateTarget(\"new\", keyframes, options);\n        return this;\n    }\n    old(keyframes, options) {\n        this.updateTarget(\"old\", keyframes, options);\n        return this;\n    }\n    enter(keyframes, options) {\n        this.updateTarget(\"enter\", keyframes, options);\n        return this;\n    }\n    exit(keyframes, options) {\n        this.updateTarget(\"exit\", keyframes, options);\n        return this;\n    }\n    crossfade(options) {\n        this.updateTarget(\"enter\", { opacity: 1 }, options);\n        this.updateTarget(\"exit\", { opacity: 0 }, options);\n        return this;\n    }\n    updateTarget(target, keyframes, options = {}) {\n        const { currentTarget, targets } = this;\n        if (!targets.has(currentTarget)) {\n            targets.set(currentTarget, {});\n        }\n        const targetData = targets.get(currentTarget);\n        targetData[target] = { keyframes, options };\n    }\n    then(resolve, reject) {\n        return this.readyPromise.then(resolve, reject);\n    }\n}\nfunction animateView(update, defaultOptions = {}) {\n    return new ViewTransitionBuilder(update, defaultOptions);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/motion-dom/dist/es/view/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/motion-dom/dist/es/view/queue.mjs":
/*!********************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/view/queue.mjs ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   addToQueue: () => (/* binding */ addToQueue)\n/* harmony export */ });\n/* harmony import */ var motion_utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! motion-utils */ \"(ssr)/./node_modules/motion-utils/dist/es/index.mjs\");\n/* harmony import */ var _frameloop_microtask_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../frameloop/microtask.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/frameloop/microtask.mjs\");\n/* harmony import */ var _start_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./start.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/view/start.mjs\");\n\n\n\n\nlet builders = [];\nlet current = null;\nfunction next() {\n    current = null;\n    const [nextBuilder] = builders;\n    if (nextBuilder)\n        start(nextBuilder);\n}\nfunction start(builder) {\n    (0,motion_utils__WEBPACK_IMPORTED_MODULE_0__.removeItem)(builders, builder);\n    current = builder;\n    (0,_start_mjs__WEBPACK_IMPORTED_MODULE_2__.startViewAnimation)(builder).then((animation) => {\n        builder.notifyReady(animation);\n        animation.finished.finally(next);\n    });\n}\nfunction processQueue() {\n    /**\n     * Iterate backwards over the builders array. We can ignore the\n     * \"wait\" animations. If we have an interrupting animation in the\n     * queue then we need to batch all preceeding animations into it.\n     * Currently this only batches the update functions but will also\n     * need to batch the targets.\n     */\n    for (let i = builders.length - 1; i >= 0; i--) {\n        const builder = builders[i];\n        const { interrupt } = builder.options;\n        if (interrupt === \"immediate\") {\n            const batchedUpdates = builders.slice(0, i + 1).map((b) => b.update);\n            const remaining = builders.slice(i + 1);\n            builder.update = () => {\n                batchedUpdates.forEach((update) => update());\n            };\n            // Put the current builder at the front, followed by any \"wait\" builders\n            builders = [builder, ...remaining];\n            break;\n        }\n    }\n    if (!current || builders[0]?.options.interrupt === \"immediate\") {\n        next();\n    }\n}\nfunction addToQueue(builder) {\n    builders.push(builder);\n    _frameloop_microtask_mjs__WEBPACK_IMPORTED_MODULE_1__.microtask.render(processQueue);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/motion-dom/dist/es/view/queue.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/motion-dom/dist/es/view/start.mjs":
/*!********************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/view/start.mjs ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   startViewAnimation: () => (/* binding */ startViewAnimation)\n/* harmony export */ });\n/* harmony import */ var motion_utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! motion-utils */ \"(ssr)/./node_modules/motion-utils/dist/es/index.mjs\");\n/* harmony import */ var _animation_GroupAnimation_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../animation/GroupAnimation.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/animation/GroupAnimation.mjs\");\n/* harmony import */ var _animation_NativeAnimation_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../animation/NativeAnimation.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/animation/NativeAnimation.mjs\");\n/* harmony import */ var _animation_utils_get_value_transition_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../animation/utils/get-value-transition.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/animation/utils/get-value-transition.mjs\");\n/* harmony import */ var _animation_waapi_easing_map_easing_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../animation/waapi/easing/map-easing.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/animation/waapi/easing/map-easing.mjs\");\n/* harmony import */ var _animation_waapi_utils_apply_generator_mjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../animation/waapi/utils/apply-generator.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/animation/waapi/utils/apply-generator.mjs\");\n/* harmony import */ var _utils_choose_layer_type_mjs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./utils/choose-layer-type.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/view/utils/choose-layer-type.mjs\");\n/* harmony import */ var _utils_css_mjs__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./utils/css.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/view/utils/css.mjs\");\n/* harmony import */ var _utils_get_layer_name_mjs__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./utils/get-layer-name.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/view/utils/get-layer-name.mjs\");\n/* harmony import */ var _utils_get_view_animations_mjs__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./utils/get-view-animations.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/view/utils/get-view-animations.mjs\");\n/* harmony import */ var _utils_has_target_mjs__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./utils/has-target.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/view/utils/has-target.mjs\");\n\n\n\n\n\n\n\n\n\n\n\n\nconst definitionNames = [\"layout\", \"enter\", \"exit\", \"new\", \"old\"];\nfunction startViewAnimation(builder) {\n    const { update, targets, options: defaultOptions } = builder;\n    if (!document.startViewTransition) {\n        return new Promise(async (resolve) => {\n            await update();\n            resolve(new _animation_GroupAnimation_mjs__WEBPACK_IMPORTED_MODULE_1__.GroupAnimation([]));\n        });\n    }\n    // TODO: Go over existing targets and ensure they all have ids\n    /**\n     * If we don't have any animations defined for the root target,\n     * remove it from being captured.\n     */\n    if (!(0,_utils_has_target_mjs__WEBPACK_IMPORTED_MODULE_10__.hasTarget)(\"root\", targets)) {\n        _utils_css_mjs__WEBPACK_IMPORTED_MODULE_7__.css.set(\":root\", {\n            \"view-transition-name\": \"none\",\n        });\n    }\n    /**\n     * Set the timing curve to linear for all view transition layers.\n     * This gets baked into the keyframes, which can't be changed\n     * without breaking the generated animation.\n     *\n     * This allows us to set easing via updateTiming - which can be changed.\n     */\n    _utils_css_mjs__WEBPACK_IMPORTED_MODULE_7__.css.set(\"::view-transition-group(*), ::view-transition-old(*), ::view-transition-new(*)\", { \"animation-timing-function\": \"linear !important\" });\n    _utils_css_mjs__WEBPACK_IMPORTED_MODULE_7__.css.commit(); // Write\n    const transition = document.startViewTransition(async () => {\n        await update();\n        // TODO: Go over new targets and ensure they all have ids\n    });\n    transition.finished.finally(() => {\n        _utils_css_mjs__WEBPACK_IMPORTED_MODULE_7__.css.remove(); // Write\n    });\n    return new Promise((resolve) => {\n        transition.ready.then(() => {\n            const generatedViewAnimations = (0,_utils_get_view_animations_mjs__WEBPACK_IMPORTED_MODULE_9__.getViewAnimations)();\n            const animations = [];\n            /**\n             * Create animations for each of our explicitly-defined subjects.\n             */\n            targets.forEach((definition, target) => {\n                // TODO: If target is not \"root\", resolve elements\n                // and iterate over each\n                for (const key of definitionNames) {\n                    if (!definition[key])\n                        continue;\n                    const { keyframes, options } = definition[key];\n                    for (let [valueName, valueKeyframes] of Object.entries(keyframes)) {\n                        if (!valueKeyframes)\n                            continue;\n                        const valueOptions = {\n                            ...(0,_animation_utils_get_value_transition_mjs__WEBPACK_IMPORTED_MODULE_3__.getValueTransition)(defaultOptions, valueName),\n                            ...(0,_animation_utils_get_value_transition_mjs__WEBPACK_IMPORTED_MODULE_3__.getValueTransition)(options, valueName),\n                        };\n                        const type = (0,_utils_choose_layer_type_mjs__WEBPACK_IMPORTED_MODULE_6__.chooseLayerType)(key);\n                        /**\n                         * If this is an opacity animation, and keyframes are not an array,\n                         * we need to convert them into an array and set an initial value.\n                         */\n                        if (valueName === \"opacity\" &&\n                            !Array.isArray(valueKeyframes)) {\n                            const initialValue = type === \"new\" ? 0 : 1;\n                            valueKeyframes = [initialValue, valueKeyframes];\n                        }\n                        /**\n                         * Resolve stagger function if provided.\n                         */\n                        if (typeof valueOptions.delay === \"function\") {\n                            valueOptions.delay = valueOptions.delay(0, 1);\n                        }\n                        const animation = new _animation_NativeAnimation_mjs__WEBPACK_IMPORTED_MODULE_2__.NativeAnimation({\n                            element: document.documentElement,\n                            name: valueName,\n                            pseudoElement: `::view-transition-${type}(${target})`,\n                            keyframes: valueKeyframes,\n                            transition: valueOptions,\n                        });\n                        animations.push(animation);\n                    }\n                }\n            });\n            /**\n             * Handle browser generated animations\n             */\n            for (const animation of generatedViewAnimations) {\n                if (animation.playState === \"finished\")\n                    continue;\n                const { effect } = animation;\n                if (!effect || !(effect instanceof KeyframeEffect))\n                    continue;\n                const { pseudoElement } = effect;\n                if (!pseudoElement)\n                    continue;\n                const name = (0,_utils_get_layer_name_mjs__WEBPACK_IMPORTED_MODULE_8__.getLayerName)(pseudoElement);\n                if (!name)\n                    continue;\n                const targetDefinition = targets.get(name.layer);\n                if (!targetDefinition) {\n                    /**\n                     * If transition name is group then update the timing of the animation\n                     * whereas if it's old or new then we could possibly replace it using\n                     * the above method.\n                     */\n                    const transitionName = name.type === \"group\" ? \"layout\" : \"\";\n                    let animationTransition = {\n                        ...(0,_animation_utils_get_value_transition_mjs__WEBPACK_IMPORTED_MODULE_3__.getValueTransition)(defaultOptions, transitionName),\n                    };\n                    animationTransition.duration && (animationTransition.duration = (0,motion_utils__WEBPACK_IMPORTED_MODULE_0__.secondsToMilliseconds)(animationTransition.duration));\n                    animationTransition =\n                        (0,_animation_waapi_utils_apply_generator_mjs__WEBPACK_IMPORTED_MODULE_5__.applyGeneratorOptions)(animationTransition);\n                    const easing = (0,_animation_waapi_easing_map_easing_mjs__WEBPACK_IMPORTED_MODULE_4__.mapEasingToNativeEasing)(animationTransition.ease, animationTransition.duration);\n                    effect.updateTiming({\n                        delay: (0,motion_utils__WEBPACK_IMPORTED_MODULE_0__.secondsToMilliseconds)(animationTransition.delay ?? 0),\n                        duration: animationTransition.duration,\n                        easing,\n                    });\n                    animations.push(new _animation_NativeAnimation_mjs__WEBPACK_IMPORTED_MODULE_2__.NativeAnimation({ animation }));\n                }\n                else if (hasOpacity(targetDefinition, \"enter\") &&\n                    hasOpacity(targetDefinition, \"exit\") &&\n                    effect\n                        .getKeyframes()\n                        .some((keyframe) => keyframe.mixBlendMode)) {\n                    animations.push(new _animation_NativeAnimation_mjs__WEBPACK_IMPORTED_MODULE_2__.NativeAnimation({ animation }));\n                }\n                else {\n                    animation.cancel();\n                }\n            }\n            resolve(new _animation_GroupAnimation_mjs__WEBPACK_IMPORTED_MODULE_1__.GroupAnimation(animations));\n        });\n    });\n}\nfunction hasOpacity(target, key) {\n    return target?.[key]?.keyframes.opacity;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/motion-dom/dist/es/view/start.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/motion-dom/dist/es/view/utils/choose-layer-type.mjs":
/*!**************************************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/view/utils/choose-layer-type.mjs ***!
  \**************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   chooseLayerType: () => (/* binding */ chooseLayerType)\n/* harmony export */ });\nfunction chooseLayerType(valueName) {\n    if (valueName === \"layout\")\n        return \"group\";\n    if (valueName === \"enter\" || valueName === \"new\")\n        return \"new\";\n    if (valueName === \"exit\" || valueName === \"old\")\n        return \"old\";\n    return \"group\";\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbW90aW9uLWRvbS9kaXN0L2VzL3ZpZXcvdXRpbHMvY2hvb3NlLWxheWVyLXR5cGUubWpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRTJCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHNoaXZhXFxPbmVEcml2ZVxcRGVza3RvcFxcbWtcXENhc2htaW5kZXItLS1Nb25leS1NYW5hZ2VtZW50LUFwcFxcbm9kZV9tb2R1bGVzXFxtb3Rpb24tZG9tXFxkaXN0XFxlc1xcdmlld1xcdXRpbHNcXGNob29zZS1sYXllci10eXBlLm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJmdW5jdGlvbiBjaG9vc2VMYXllclR5cGUodmFsdWVOYW1lKSB7XG4gICAgaWYgKHZhbHVlTmFtZSA9PT0gXCJsYXlvdXRcIilcbiAgICAgICAgcmV0dXJuIFwiZ3JvdXBcIjtcbiAgICBpZiAodmFsdWVOYW1lID09PSBcImVudGVyXCIgfHwgdmFsdWVOYW1lID09PSBcIm5ld1wiKVxuICAgICAgICByZXR1cm4gXCJuZXdcIjtcbiAgICBpZiAodmFsdWVOYW1lID09PSBcImV4aXRcIiB8fCB2YWx1ZU5hbWUgPT09IFwib2xkXCIpXG4gICAgICAgIHJldHVybiBcIm9sZFwiO1xuICAgIHJldHVybiBcImdyb3VwXCI7XG59XG5cbmV4cG9ydCB7IGNob29zZUxheWVyVHlwZSB9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/motion-dom/dist/es/view/utils/choose-layer-type.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/motion-dom/dist/es/view/utils/css.mjs":
/*!************************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/view/utils/css.mjs ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   css: () => (/* binding */ css)\n/* harmony export */ });\nlet pendingRules = {};\nlet style = null;\nconst css = {\n    set: (selector, values) => {\n        pendingRules[selector] = values;\n    },\n    commit: () => {\n        if (!style) {\n            style = document.createElement(\"style\");\n            style.id = \"motion-view\";\n        }\n        let cssText = \"\";\n        for (const selector in pendingRules) {\n            const rule = pendingRules[selector];\n            cssText += `${selector} {\\n`;\n            for (const [property, value] of Object.entries(rule)) {\n                cssText += `  ${property}: ${value};\\n`;\n            }\n            cssText += \"}\\n\";\n        }\n        style.textContent = cssText;\n        document.head.appendChild(style);\n        pendingRules = {};\n    },\n    remove: () => {\n        if (style && style.parentElement) {\n            style.parentElement.removeChild(style);\n        }\n    },\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbW90aW9uLWRvbS9kaXN0L2VzL3ZpZXcvdXRpbHMvY3NzLm1qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMEJBQTBCLFdBQVc7QUFDckM7QUFDQSxnQ0FBZ0MsU0FBUyxJQUFJLE9BQU87QUFDcEQ7QUFDQSx5QkFBeUI7QUFDekI7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7O0FBRWUiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcc2hpdmFcXE9uZURyaXZlXFxEZXNrdG9wXFxta1xcQ2FzaG1pbmRlci0tLU1vbmV5LU1hbmFnZW1lbnQtQXBwXFxub2RlX21vZHVsZXNcXG1vdGlvbi1kb21cXGRpc3RcXGVzXFx2aWV3XFx1dGlsc1xcY3NzLm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJsZXQgcGVuZGluZ1J1bGVzID0ge307XG5sZXQgc3R5bGUgPSBudWxsO1xuY29uc3QgY3NzID0ge1xuICAgIHNldDogKHNlbGVjdG9yLCB2YWx1ZXMpID0+IHtcbiAgICAgICAgcGVuZGluZ1J1bGVzW3NlbGVjdG9yXSA9IHZhbHVlcztcbiAgICB9LFxuICAgIGNvbW1pdDogKCkgPT4ge1xuICAgICAgICBpZiAoIXN0eWxlKSB7XG4gICAgICAgICAgICBzdHlsZSA9IGRvY3VtZW50LmNyZWF0ZUVsZW1lbnQoXCJzdHlsZVwiKTtcbiAgICAgICAgICAgIHN0eWxlLmlkID0gXCJtb3Rpb24tdmlld1wiO1xuICAgICAgICB9XG4gICAgICAgIGxldCBjc3NUZXh0ID0gXCJcIjtcbiAgICAgICAgZm9yIChjb25zdCBzZWxlY3RvciBpbiBwZW5kaW5nUnVsZXMpIHtcbiAgICAgICAgICAgIGNvbnN0IHJ1bGUgPSBwZW5kaW5nUnVsZXNbc2VsZWN0b3JdO1xuICAgICAgICAgICAgY3NzVGV4dCArPSBgJHtzZWxlY3Rvcn0ge1xcbmA7XG4gICAgICAgICAgICBmb3IgKGNvbnN0IFtwcm9wZXJ0eSwgdmFsdWVdIG9mIE9iamVjdC5lbnRyaWVzKHJ1bGUpKSB7XG4gICAgICAgICAgICAgICAgY3NzVGV4dCArPSBgICAke3Byb3BlcnR5fTogJHt2YWx1ZX07XFxuYDtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGNzc1RleHQgKz0gXCJ9XFxuXCI7XG4gICAgICAgIH1cbiAgICAgICAgc3R5bGUudGV4dENvbnRlbnQgPSBjc3NUZXh0O1xuICAgICAgICBkb2N1bWVudC5oZWFkLmFwcGVuZENoaWxkKHN0eWxlKTtcbiAgICAgICAgcGVuZGluZ1J1bGVzID0ge307XG4gICAgfSxcbiAgICByZW1vdmU6ICgpID0+IHtcbiAgICAgICAgaWYgKHN0eWxlICYmIHN0eWxlLnBhcmVudEVsZW1lbnQpIHtcbiAgICAgICAgICAgIHN0eWxlLnBhcmVudEVsZW1lbnQucmVtb3ZlQ2hpbGQoc3R5bGUpO1xuICAgICAgICB9XG4gICAgfSxcbn07XG5cbmV4cG9ydCB7IGNzcyB9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/motion-dom/dist/es/view/utils/css.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/motion-dom/dist/es/view/utils/get-layer-name.mjs":
/*!***********************************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/view/utils/get-layer-name.mjs ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getLayerName: () => (/* binding */ getLayerName)\n/* harmony export */ });\nfunction getLayerName(pseudoElement) {\n    const match = pseudoElement.match(/::view-transition-(old|new|group|image-pair)\\((.*?)\\)/);\n    if (!match)\n        return null;\n    return { layer: match[2], type: match[1] };\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbW90aW9uLWRvbS9kaXN0L2VzL3ZpZXcvdXRpbHMvZ2V0LWxheWVyLW5hbWUubWpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGFBQWE7QUFDYjs7QUFFd0IiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcc2hpdmFcXE9uZURyaXZlXFxEZXNrdG9wXFxta1xcQ2FzaG1pbmRlci0tLU1vbmV5LU1hbmFnZW1lbnQtQXBwXFxub2RlX21vZHVsZXNcXG1vdGlvbi1kb21cXGRpc3RcXGVzXFx2aWV3XFx1dGlsc1xcZ2V0LWxheWVyLW5hbWUubWpzIl0sInNvdXJjZXNDb250ZW50IjpbImZ1bmN0aW9uIGdldExheWVyTmFtZShwc2V1ZG9FbGVtZW50KSB7XG4gICAgY29uc3QgbWF0Y2ggPSBwc2V1ZG9FbGVtZW50Lm1hdGNoKC86OnZpZXctdHJhbnNpdGlvbi0ob2xkfG5ld3xncm91cHxpbWFnZS1wYWlyKVxcKCguKj8pXFwpLyk7XG4gICAgaWYgKCFtYXRjaClcbiAgICAgICAgcmV0dXJuIG51bGw7XG4gICAgcmV0dXJuIHsgbGF5ZXI6IG1hdGNoWzJdLCB0eXBlOiBtYXRjaFsxXSB9O1xufVxuXG5leHBvcnQgeyBnZXRMYXllck5hbWUgfTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/motion-dom/dist/es/view/utils/get-layer-name.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/motion-dom/dist/es/view/utils/get-view-animations.mjs":
/*!****************************************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/view/utils/get-view-animations.mjs ***!
  \****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getViewAnimations: () => (/* binding */ getViewAnimations)\n/* harmony export */ });\nfunction filterViewAnimations(animation) {\n    const { effect } = animation;\n    if (!effect)\n        return false;\n    return (effect.target === document.documentElement &&\n        effect.pseudoElement?.startsWith(\"::view-transition\"));\n}\nfunction getViewAnimations() {\n    return document.getAnimations().filter(filterViewAnimations);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbW90aW9uLWRvbS9kaXN0L2VzL3ZpZXcvdXRpbHMvZ2V0LXZpZXctYW5pbWF0aW9ucy5tanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0EsWUFBWSxTQUFTO0FBQ3JCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRTZCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHNoaXZhXFxPbmVEcml2ZVxcRGVza3RvcFxcbWtcXENhc2htaW5kZXItLS1Nb25leS1NYW5hZ2VtZW50LUFwcFxcbm9kZV9tb2R1bGVzXFxtb3Rpb24tZG9tXFxkaXN0XFxlc1xcdmlld1xcdXRpbHNcXGdldC12aWV3LWFuaW1hdGlvbnMubWpzIl0sInNvdXJjZXNDb250ZW50IjpbImZ1bmN0aW9uIGZpbHRlclZpZXdBbmltYXRpb25zKGFuaW1hdGlvbikge1xuICAgIGNvbnN0IHsgZWZmZWN0IH0gPSBhbmltYXRpb247XG4gICAgaWYgKCFlZmZlY3QpXG4gICAgICAgIHJldHVybiBmYWxzZTtcbiAgICByZXR1cm4gKGVmZmVjdC50YXJnZXQgPT09IGRvY3VtZW50LmRvY3VtZW50RWxlbWVudCAmJlxuICAgICAgICBlZmZlY3QucHNldWRvRWxlbWVudD8uc3RhcnRzV2l0aChcIjo6dmlldy10cmFuc2l0aW9uXCIpKTtcbn1cbmZ1bmN0aW9uIGdldFZpZXdBbmltYXRpb25zKCkge1xuICAgIHJldHVybiBkb2N1bWVudC5nZXRBbmltYXRpb25zKCkuZmlsdGVyKGZpbHRlclZpZXdBbmltYXRpb25zKTtcbn1cblxuZXhwb3J0IHsgZ2V0Vmlld0FuaW1hdGlvbnMgfTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/motion-dom/dist/es/view/utils/get-view-animations.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/motion-dom/dist/es/view/utils/has-target.mjs":
/*!*******************************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/view/utils/has-target.mjs ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   hasTarget: () => (/* binding */ hasTarget)\n/* harmony export */ });\nfunction hasTarget(target, targets) {\n    return targets.has(target) && Object.keys(targets.get(target)).length > 0;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbW90aW9uLWRvbS9kaXN0L2VzL3ZpZXcvdXRpbHMvaGFzLXRhcmdldC5tanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0E7QUFDQTs7QUFFcUIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcc2hpdmFcXE9uZURyaXZlXFxEZXNrdG9wXFxta1xcQ2FzaG1pbmRlci0tLU1vbmV5LU1hbmFnZW1lbnQtQXBwXFxub2RlX21vZHVsZXNcXG1vdGlvbi1kb21cXGRpc3RcXGVzXFx2aWV3XFx1dGlsc1xcaGFzLXRhcmdldC5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiZnVuY3Rpb24gaGFzVGFyZ2V0KHRhcmdldCwgdGFyZ2V0cykge1xuICAgIHJldHVybiB0YXJnZXRzLmhhcyh0YXJnZXQpICYmIE9iamVjdC5rZXlzKHRhcmdldHMuZ2V0KHRhcmdldCkpLmxlbmd0aCA+IDA7XG59XG5cbmV4cG9ydCB7IGhhc1RhcmdldCB9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/motion-dom/dist/es/view/utils/has-target.mjs\n");

/***/ })

};
;