/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5Cshiva%5COneDrive%5CDesktop%5Cmk%5CCashminder---Money-Management-App%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cshiva%5COneDrive%5CDesktop%5Cmk%5CCashminder---Money-Management-App&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5Cshiva%5COneDrive%5CDesktop%5Cmk%5CCashminder---Money-Management-App%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cshiva%5COneDrive%5CDesktop%5Cmk%5CCashminder---Money-Management-App&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\page.tsx\"],\n          metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n        }]\n      },\n        {\n        'layout': [module0, \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5Cshiva%5COneDrive%5CDesktop%5Cmk%5CCashminder---Money-Management-App%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cshiva%5COneDrive%5CDesktop%5Cmk%5CCashminder---Money-Management-App&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Orbitron%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-orbitron%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22orbitron%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Rajdhani%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-rajdhani%5C%22%2C%5C%22weight%5C%22%3A%5B%5C%22300%5C%22%2C%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22600%5C%22%2C%5C%22700%5C%22%5D%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22rajdhani%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Audiowide%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-audiowide%5C%22%2C%5C%22weight%5C%22%3A%5B%5C%22400%5C%22%5D%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22audiowide%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5CFuturisticNavbar.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Csrc%5C%5Ccomponents%5C%5CThemeWrapper.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Orbitron%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-orbitron%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22orbitron%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Rajdhani%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-rajdhani%5C%22%2C%5C%22weight%5C%22%3A%5B%5C%22300%5C%22%2C%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22600%5C%22%2C%5C%22700%5C%22%5D%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22rajdhani%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Audiowide%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-audiowide%5C%22%2C%5C%22weight%5C%22%3A%5B%5C%22400%5C%22%5D%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22audiowide%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5CFuturisticNavbar.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Csrc%5C%5Ccomponents%5C%5CThemeWrapper.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/layout/FuturisticNavbar.tsx */ \"(rsc)/./src/components/layout/FuturisticNavbar.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ThemeWrapper.tsx */ \"(rsc)/./src/components/ThemeWrapper.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Orbitron%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-orbitron%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22orbitron%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Rajdhani%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-rajdhani%5C%22%2C%5C%22weight%5C%22%3A%5B%5C%22300%5C%22%2C%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22600%5C%22%2C%5C%22700%5C%22%5D%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22rajdhani%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Audiowide%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-audiowide%5C%22%2C%5C%22weight%5C%22%3A%5B%5C%22400%5C%22%5D%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22audiowide%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5CFuturisticNavbar.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Csrc%5C%5Ccomponents%5C%5CThemeWrapper.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q3NoaXZhJTVDJTVDT25lRHJpdmUlNUMlNUNEZXNrdG9wJTVDJTVDbWslNUMlNUNDYXNobWluZGVyLS0tTW9uZXktTWFuYWdlbWVudC1BcHAlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsZ0pBQW1JIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxzaGl2YVxcXFxPbmVEcml2ZVxcXFxEZXNrdG9wXFxcXG1rXFxcXENhc2htaW5kZXItLS1Nb25leS1NYW5hZ2VtZW50LUFwcFxcXFxzcmNcXFxcYXBwXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcc2hpdmFcXE9uZURyaXZlXFxEZXNrdG9wXFxta1xcQ2FzaG1pbmRlci0tLU1vbmV5LU1hbmFnZW1lbnQtQXBwXFxzcmNcXGFwcFxcZmF2aWNvbi5pY28/X19uZXh0X21ldGFkYXRhX18iXSwic291cmNlc0NvbnRlbnQiOlsiICBpbXBvcnQgeyBmaWxsTWV0YWRhdGFTZWdtZW50IH0gZnJvbSAnbmV4dC9kaXN0L2xpYi9tZXRhZGF0YS9nZXQtbWV0YWRhdGEtcm91dGUnXG5cbiAgZXhwb3J0IGRlZmF1bHQgYXN5bmMgKHByb3BzKSA9PiB7XG4gICAgY29uc3QgaW1hZ2VEYXRhID0ge1widHlwZVwiOlwiaW1hZ2UveC1pY29uXCIsXCJzaXplc1wiOlwiMTZ4MTZcIn1cbiAgICBjb25zdCBpbWFnZVVybCA9IGZpbGxNZXRhZGF0YVNlZ21lbnQoXCIuXCIsIGF3YWl0IHByb3BzLnBhcmFtcywgXCJmYXZpY29uLmljb1wiKVxuXG4gICAgcmV0dXJuIFt7XG4gICAgICAuLi5pbWFnZURhdGEsXG4gICAgICB1cmw6IGltYWdlVXJsICsgXCJcIixcbiAgICB9XVxuICB9Il0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"94bb89cc64ba\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHNoaXZhXFxPbmVEcml2ZVxcRGVza3RvcFxcbWtcXENhc2htaW5kZXItLS1Nb25leS1NYW5hZ2VtZW50LUFwcFxcc3JjXFxhcHBcXGdsb2JhbHMuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiOTRiYjg5Y2M2NGJhXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_variable_font_inter_subsets_latin_display_swap_variableName_inter___WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"variable\":\"--font-inter\",\"subsets\":[\"latin\"],\"display\":\"swap\"}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-inter\\\",\\\"subsets\\\":[\\\"latin\\\"],\\\"display\\\":\\\"swap\\\"}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_variable_font_inter_subsets_latin_display_swap_variableName_inter___WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_variable_font_inter_subsets_latin_display_swap_variableName_inter___WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist\",\"arguments\":[{\"variable\":\"--font-geist-sans\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistSans\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Geist\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-sans\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistSans\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist_Mono\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistMono\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Geist_Mono\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-mono\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistMono\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Orbitron_arguments_variable_font_orbitron_subsets_latin_display_swap_variableName_orbitron___WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Orbitron\",\"arguments\":[{\"variable\":\"--font-orbitron\",\"subsets\":[\"latin\"],\"display\":\"swap\"}],\"variableName\":\"orbitron\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Orbitron\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-orbitron\\\",\\\"subsets\\\":[\\\"latin\\\"],\\\"display\\\":\\\"swap\\\"}],\\\"variableName\\\":\\\"orbitron\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Orbitron_arguments_variable_font_orbitron_subsets_latin_display_swap_variableName_orbitron___WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Orbitron_arguments_variable_font_orbitron_subsets_latin_display_swap_variableName_orbitron___WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Rajdhani_arguments_variable_font_rajdhani_weight_300_400_500_600_700_subsets_latin_display_swap_variableName_rajdhani___WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Rajdhani\",\"arguments\":[{\"variable\":\"--font-rajdhani\",\"weight\":[\"300\",\"400\",\"500\",\"600\",\"700\"],\"subsets\":[\"latin\"],\"display\":\"swap\"}],\"variableName\":\"rajdhani\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Rajdhani\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-rajdhani\\\",\\\"weight\\\":[\\\"300\\\",\\\"400\\\",\\\"500\\\",\\\"600\\\",\\\"700\\\"],\\\"subsets\\\":[\\\"latin\\\"],\\\"display\\\":\\\"swap\\\"}],\\\"variableName\\\":\\\"rajdhani\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Rajdhani_arguments_variable_font_rajdhani_weight_300_400_500_600_700_subsets_latin_display_swap_variableName_rajdhani___WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Rajdhani_arguments_variable_font_rajdhani_weight_300_400_500_600_700_subsets_latin_display_swap_variableName_rajdhani___WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Audiowide_arguments_variable_font_audiowide_weight_400_subsets_latin_display_swap_variableName_audiowide___WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Audiowide\",\"arguments\":[{\"variable\":\"--font-audiowide\",\"weight\":[\"400\"],\"subsets\":[\"latin\"],\"display\":\"swap\"}],\"variableName\":\"audiowide\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Audiowide\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-audiowide\\\",\\\"weight\\\":[\\\"400\\\"],\\\"subsets\\\":[\\\"latin\\\"],\\\"display\\\":\\\"swap\\\"}],\\\"variableName\\\":\\\"audiowide\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Audiowide_arguments_variable_font_audiowide_weight_400_subsets_latin_display_swap_variableName_audiowide___WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Audiowide_arguments_variable_font_audiowide_weight_400_subsets_latin_display_swap_variableName_audiowide___WEBPACK_IMPORTED_MODULE_9__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _components_layout_FuturisticNavbar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/layout/FuturisticNavbar */ \"(rsc)/./src/components/layout/FuturisticNavbar.tsx\");\n/* harmony import */ var _components_ThemeWrapper__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ThemeWrapper */ \"(rsc)/./src/components/ThemeWrapper.tsx\");\n\n\n\n\n\n\n\n\n\n\nconst metadata = {\n    title: \"Cashminder - Personal Finance Manager\",\n    description: \"Track your expenses, manage your budget, and achieve your financial goals\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"script\", {\n                    dangerouslySetInnerHTML: {\n                        __html: `\n            // Block Sentry requests to prevent console errors\n            const originalFetch = window.fetch;\n            window.fetch = function(url, options) {\n              if (url && typeof url === 'string' && url.includes('sentry')) {\n                console.log('Blocked Sentry request:', url);\n                return Promise.resolve(new Response('', { status: 200 }));\n              }\n              return originalFetch.apply(this, arguments);\n            };\n          `\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 62,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 57,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_4___default().variable)} ${(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_5___default().variable)} ${(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_variable_font_inter_subsets_latin_display_swap_variableName_inter___WEBPACK_IMPORTED_MODULE_6___default().variable)} ${(next_font_google_target_css_path_src_app_layout_tsx_import_Orbitron_arguments_variable_font_orbitron_subsets_latin_display_swap_variableName_orbitron___WEBPACK_IMPORTED_MODULE_7___default().variable)} ${(next_font_google_target_css_path_src_app_layout_tsx_import_Rajdhani_arguments_variable_font_rajdhani_weight_300_400_500_600_700_subsets_latin_display_swap_variableName_rajdhani___WEBPACK_IMPORTED_MODULE_8___default().variable)} ${(next_font_google_target_css_path_src_app_layout_tsx_import_Audiowide_arguments_variable_font_audiowide_weight_400_subsets_latin_display_swap_variableName_audiowide___WEBPACK_IMPORTED_MODULE_9___default().variable)} font-sans antialiased bg-gray-50 dark:bg-gray-900 min-h-screen transition-colors duration-300`,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ThemeWrapper__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col min-h-screen\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-grow\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_FuturisticNavbar__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\layout.tsx\",\n                                    lineNumber: 83,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                                    className: \"py-10\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"px-4 mx-auto max-w-7xl sm:px-6 lg:px-8\",\n                                        children: children\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\layout.tsx\",\n                                        lineNumber: 85,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\layout.tsx\",\n                                    lineNumber: 84,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\layout.tsx\",\n                            lineNumber: 81,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 80,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 79,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 76,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 56,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\OneDrive\\Desktop\\mk\\Cashminder---Money-Management-App\\src\\app\\page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/components/ThemeWrapper.tsx":
/*!*****************************************!*\
  !*** ./src/components/ThemeWrapper.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\components\\\\ThemeWrapper.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\OneDrive\\Desktop\\mk\\Cashminder---Money-Management-App\\src\\components\\ThemeWrapper.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/components/layout/FuturisticNavbar.tsx":
/*!****************************************************!*\
  !*** ./src/components/layout/FuturisticNavbar.tsx ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\components\\\\layout\\\\FuturisticNavbar.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\OneDrive\\Desktop\\mk\\Cashminder---Money-Management-App\\src\\components\\layout\\FuturisticNavbar.tsx",
"default",
));


/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Orbitron%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-orbitron%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22orbitron%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Rajdhani%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-rajdhani%5C%22%2C%5C%22weight%5C%22%3A%5B%5C%22300%5C%22%2C%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22600%5C%22%2C%5C%22700%5C%22%5D%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22rajdhani%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Audiowide%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-audiowide%5C%22%2C%5C%22weight%5C%22%3A%5B%5C%22400%5C%22%5D%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22audiowide%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5CFuturisticNavbar.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Csrc%5C%5Ccomponents%5C%5CThemeWrapper.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Orbitron%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-orbitron%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22orbitron%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Rajdhani%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-rajdhani%5C%22%2C%5C%22weight%5C%22%3A%5B%5C%22300%5C%22%2C%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22600%5C%22%2C%5C%22700%5C%22%5D%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22rajdhani%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Audiowide%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-audiowide%5C%22%2C%5C%22weight%5C%22%3A%5B%5C%22400%5C%22%5D%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22audiowide%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5CFuturisticNavbar.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Csrc%5C%5Ccomponents%5C%5CThemeWrapper.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/layout/FuturisticNavbar.tsx */ \"(ssr)/./src/components/layout/FuturisticNavbar.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ThemeWrapper.tsx */ \"(ssr)/./src/components/ThemeWrapper.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Orbitron%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-orbitron%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22orbitron%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Rajdhani%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-rajdhani%5C%22%2C%5C%22weight%5C%22%3A%5B%5C%22300%5C%22%2C%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22600%5C%22%2C%5C%22700%5C%22%5D%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22rajdhani%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Audiowide%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-audiowide%5C%22%2C%5C%22weight%5C%22%3A%5B%5C%22400%5C%22%5D%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22audiowide%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5CFuturisticNavbar.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Csrc%5C%5Ccomponents%5C%5CThemeWrapper.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(ssr)/./src/app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q3NoaXZhJTVDJTVDT25lRHJpdmUlNUMlNUNEZXNrdG9wJTVDJTVDbWslNUMlNUNDYXNobWluZGVyLS0tTW9uZXktTWFuYWdlbWVudC1BcHAlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsZ0pBQW1JIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxzaGl2YVxcXFxPbmVEcml2ZVxcXFxEZXNrdG9wXFxcXG1rXFxcXENhc2htaW5kZXItLS1Nb25leS1NYW5hZ2VtZW50LUFwcFxcXFxzcmNcXFxcYXBwXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshiva%5C%5COneDrive%5C%5CDesktop%5C%5Cmk%5C%5CCashminder---Money-Management-App%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_FiArrowRight_FiBarChart2_FiDollarSign_FiFileText_FiGithub_FiLinkedin_FiMail_FiPieChart_FiShield_FiTarget_FiTrendingUp_FiTwitter_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=FiArrowRight,FiBarChart2,FiDollarSign,FiFileText,FiGithub,FiLinkedin,FiMail,FiPieChart,FiShield,FiTarget,FiTrendingUp,FiTwitter,FiZap!=!react-icons/fi */ \"(ssr)/./node_modules/react-icons/fi/index.mjs\");\n/* harmony import */ var _context_ThemeContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/context/ThemeContext */ \"(ssr)/./src/context/ThemeContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n// Animated number counter component\nconst AnimatedCounter = ({ value, duration = 0.5 })=>{\n    const [count, setCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AnimatedCounter.useEffect\": ()=>{\n            let start = 0;\n            const end = value;\n            const totalMiliseconds = duration * 1000;\n            // Use a faster increment for large numbers\n            const increment = Math.max(1, Math.floor(end / 30));\n            const incrementTime = totalMiliseconds / (end / increment);\n            const timer = setInterval({\n                \"AnimatedCounter.useEffect.timer\": ()=>{\n                    start = Math.min(end, start + increment);\n                    setCount(start);\n                    if (start >= end) clearInterval(timer);\n                }\n            }[\"AnimatedCounter.useEffect.timer\"], incrementTime);\n            return ({\n                \"AnimatedCounter.useEffect\": ()=>clearInterval(timer)\n            })[\"AnimatedCounter.useEffect\"];\n        }\n    }[\"AnimatedCounter.useEffect\"], [\n        value,\n        duration\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n        children: count.toLocaleString()\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 35,\n        columnNumber: 10\n    }, undefined);\n};\nfunction Home() {\n    const { theme } = (0,_context_ThemeContext__WEBPACK_IMPORTED_MODULE_3__.useTheme)();\n    const isDark = theme === 'dark';\n    const [isVisible, setIsVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [userData, setUserData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        totalBalance: 0,\n        income: 0,\n        expenses: 0,\n        savingsGoal: {\n            current: 0,\n            target: 10000,\n            percentage: 0\n        }\n    });\n    const [isLoggedIn, setIsLoggedIn] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            setIsVisible(true);\n            // Check if user is logged in and get their data\n            const storedUser = localStorage.getItem('cashminder_user');\n            if (storedUser) {\n                setIsLoggedIn(true);\n                // In a real app, we would fetch the user's data from the API\n                // For now, we'll just use zeros for a new user\n                setUserData({\n                    totalBalance: 0,\n                    income: 0,\n                    expenses: 0,\n                    savingsGoal: {\n                        current: 0,\n                        target: 10000,\n                        percentage: 0\n                    }\n                });\n            }\n        }\n    }[\"Home.useEffect\"], []);\n    const containerVariants = {\n        hidden: {\n            opacity: 0\n        },\n        visible: {\n            opacity: 1,\n            transition: {\n                staggerChildren: 0.1,\n                delayChildren: 0.3\n            }\n        }\n    };\n    const itemVariants = {\n        hidden: {\n            y: 20,\n            opacity: 0\n        },\n        visible: {\n            y: 0,\n            opacity: 1,\n            transition: {\n                type: 'spring',\n                stiffness: 100\n            }\n        }\n    };\n    const features = [\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiArrowRight_FiBarChart2_FiDollarSign_FiFileText_FiGithub_FiLinkedin_FiMail_FiPieChart_FiShield_FiTarget_FiTrendingUp_FiTwitter_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__.FiBarChart2, {\n                className: \"w-8 h-8\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 99,\n                columnNumber: 13\n            }, this),\n            title: \"AI-Powered Analytics\",\n            description: \"Harness the power of machine learning to predict spending patterns and optimize your financial decisions.\"\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiArrowRight_FiBarChart2_FiDollarSign_FiFileText_FiGithub_FiLinkedin_FiMail_FiPieChart_FiShield_FiTarget_FiTrendingUp_FiTwitter_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__.FiPieChart, {\n                className: \"w-8 h-8\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 104,\n                columnNumber: 13\n            }, this),\n            title: \"Dynamic Budgeting\",\n            description: \"Real-time budget tracking with intelligent categorization and personalized alerts.\"\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiArrowRight_FiBarChart2_FiDollarSign_FiFileText_FiGithub_FiLinkedin_FiMail_FiPieChart_FiShield_FiTarget_FiTrendingUp_FiTwitter_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__.FiTarget, {\n                className: \"w-8 h-8\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 109,\n                columnNumber: 13\n            }, this),\n            title: \"Smart Goal Tracking\",\n            description: \"Set and achieve financial goals with adaptive recommendations based on your spending habits.\"\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiArrowRight_FiBarChart2_FiDollarSign_FiFileText_FiGithub_FiLinkedin_FiMail_FiPieChart_FiShield_FiTarget_FiTrendingUp_FiTwitter_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__.FiTrendingUp, {\n                className: \"w-8 h-8\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 114,\n                columnNumber: 13\n            }, this),\n            title: \"Investment Insights\",\n            description: \"Track your investments and receive AI-powered recommendations for portfolio optimization.\"\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiArrowRight_FiBarChart2_FiDollarSign_FiFileText_FiGithub_FiLinkedin_FiMail_FiPieChart_FiShield_FiTarget_FiTrendingUp_FiTwitter_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__.FiShield, {\n                className: \"w-8 h-8\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 119,\n                columnNumber: 13\n            }, this),\n            title: \"Bank-Level Security\",\n            description: \"Your financial data is protected with enterprise-grade encryption and security protocols.\"\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiArrowRight_FiBarChart2_FiDollarSign_FiFileText_FiGithub_FiLinkedin_FiMail_FiPieChart_FiShield_FiTarget_FiTrendingUp_FiTwitter_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__.FiZap, {\n                className: \"w-8 h-8\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 124,\n                columnNumber: 13\n            }, this),\n            title: \"Real-Time Notifications\",\n            description: \"Instant alerts for unusual spending, bill payments, and financial opportunities.\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-0\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-light-bg dark:bg-space-gradient animate-gradient\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 135,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-[url('/grid.svg')] bg-center [mask-image:linear-gradient(180deg,white,rgba(255,255,255,0))] opacity-[0.03] dark:opacity-[0.1]\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 138,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-primary via-secondary to-primary animate-gradient\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 141,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-1/4 left-1/4 w-96 h-96 bg-primary/10 dark:bg-primary/20 rounded-full blur-3xl\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 144,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                        className: \"absolute bottom-1/3 right-1/4 w-64 h-64 bg-secondary/10 dark:bg-secondary/20 rounded-full blur-3xl\",\n                        animate: {\n                            scale: [\n                                1,\n                                1.2,\n                                1\n                            ],\n                            opacity: [\n                                0.1,\n                                0.2,\n                                0.1\n                            ]\n                        },\n                        transition: {\n                            duration: 8,\n                            repeat: Infinity,\n                            ease: \"easeInOut\"\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 145,\n                        columnNumber: 9\n                    }, this),\n                    [\n                        ...Array(3)\n                    ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                            className: \"absolute h-[1px] bg-gradient-to-r from-transparent via-primary/30 to-transparent\",\n                            style: {\n                                top: `${30 + i * 20}%`,\n                                left: 0,\n                                right: 0,\n                                transformOrigin: 'center'\n                            },\n                            animate: {\n                                scaleX: [\n                                    0.5,\n                                    1.5,\n                                    0.5\n                                ],\n                                opacity: [\n                                    0.1,\n                                    0.3,\n                                    0.1\n                                ]\n                            },\n                            transition: {\n                                duration: 8 + i,\n                                repeat: Infinity,\n                                ease: \"easeInOut\",\n                                delay: i * 0.5\n                            }\n                        }, `line-${i}`, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 160,\n                            columnNumber: 11\n                        }, this))\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 133,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"relative min-h-screen flex items-center justify-center px-4 py-20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto max-w-7xl\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                        className: \"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center\",\n                        initial: \"hidden\",\n                        animate: isVisible ? \"visible\" : \"hidden\",\n                        variants: containerVariants,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                        variants: itemVariants,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"inline-block px-4 py-2 rounded-full text-sm font-medium bg-primary/10 dark:bg-primary/20 text-primary-700 dark:text-primary mb-4\",\n                                                children: \"Smart Financial Management\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 194,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"leading-tight\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"block text-4xl md:text-5xl font-orbitron font-bold text-light-text-primary dark:text-dark-text-primary letter-spacing-wide mb-2\",\n                                                        children: \"MASTER YOUR MONEY\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 198,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"block text-5xl md:text-6xl font-audiowide text-shimmer\",\n                                                        children: \"SHAPE YOUR FUTURE\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 201,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 197,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 193,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.p, {\n                                        className: \"text-xl font-rajdhani text-light-text-secondary dark:text-dark-text-secondary mt-6 max-w-2xl\",\n                                        variants: itemVariants,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-semibold text-primary\",\n                                                children: \"Visualize\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 211,\n                                                columnNumber: 17\n                                            }, this),\n                                            \" your spending patterns,\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-semibold text-secondary\",\n                                                children: \" automate\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 212,\n                                                columnNumber: 17\n                                            }, this),\n                                            \" your savings, and\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-semibold text-primary\",\n                                                children: \" unlock\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 213,\n                                                columnNumber: 17\n                                            }, this),\n                                            \" your financial potential with our AI-powered platform.\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 207,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                        className: \"flex flex-col sm:flex-row gap-4 mt-8\",\n                                        variants: itemVariants,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: isLoggedIn ? \"/dashboard\" : \"/auth\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.button, {\n                                                    className: \"group flex items-center justify-center px-8 py-4 text-lg font-medium rounded-xl text-dark-bg bg-primary hover:bg-primary-400 transition-all duration-300 shadow-md\",\n                                                    whileHover: {\n                                                        scale: 1.05,\n                                                        boxShadow: \"var(--glow-primary)\"\n                                                    },\n                                                    whileTap: {\n                                                        scale: 0.98\n                                                    },\n                                                    children: [\n                                                        isLoggedIn ? \"Go to Dashboard\" : \"Get Started\",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiArrowRight_FiBarChart2_FiDollarSign_FiFileText_FiGithub_FiLinkedin_FiMail_FiPieChart_FiShield_FiTarget_FiTrendingUp_FiTwitter_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__.FiArrowRight, {\n                                                            className: \"ml-2 group-hover:translate-x-1 transition-transform\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 230,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 221,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 220,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"#features\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.button, {\n                                                    className: \"flex items-center justify-center px-8 py-4 text-lg font-medium rounded-xl border border-light-border dark:border-dark-border text-light-text-primary dark:text-dark-text-primary hover:bg-light-accent dark:hover:bg-dark-accent transition-all duration-300\",\n                                                    whileHover: {\n                                                        scale: 1.05,\n                                                        boxShadow: \"0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.1)\"\n                                                    },\n                                                    whileTap: {\n                                                        scale: 0.98\n                                                    },\n                                                    children: \"Learn More\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 234,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 233,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 216,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 192,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                className: \"relative\",\n                                variants: itemVariants,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 bg-gradient-to-r from-primary/20 to-secondary/20 rounded-2xl blur-xl\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 252,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                        className: \"relative p-8 rounded-2xl glass-card\",\n                                        whileHover: {\n                                            y: -5,\n                                            boxShadow: \"var(--glow-primary)\"\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-center mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-xl font-semibold text-light-text-primary dark:text-dark-text-primary\",\n                                                        children: \"Financial Overview\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 261,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-primary font-medium\",\n                                                        children: \"Today\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 262,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 260,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-4 rounded-xl bg-light-card dark:bg-dark-accent border border-light-border dark:border-dark-border finance-card\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between items-center mb-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm text-light-text-secondary dark:text-dark-text-secondary\",\n                                                                        children: \"Total Balance\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 268,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiArrowRight_FiBarChart2_FiDollarSign_FiFileText_FiGithub_FiLinkedin_FiMail_FiPieChart_FiShield_FiTarget_FiTrendingUp_FiTwitter_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__.FiDollarSign, {\n                                                                        className: \"text-primary\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 269,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 267,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-2xl font-bold text-light-text-primary dark:text-dark-text-primary\",\n                                                                children: [\n                                                                    \"$\",\n                                                                    userData.totalBalance.toLocaleString()\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 271,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            userData.totalBalance > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center mt-1 text-success text-sm\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiArrowRight_FiBarChart2_FiDollarSign_FiFileText_FiGithub_FiLinkedin_FiMail_FiPieChart_FiShield_FiTarget_FiTrendingUp_FiTwitter_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__.FiTrendingUp, {\n                                                                        className: \"mr-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 276,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"Track your balance\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 277,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 275,\n                                                                columnNumber: 23\n                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center mt-1 text-secondary text-sm\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Add transactions to see your balance\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 281,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 280,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 266,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-2 gap-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"p-4 rounded-xl bg-success/10 dark:bg-success/20 border border-success/20 dark:border-success/30 finance-card\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex justify-between items-center mb-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm text-light-text-secondary dark:text-dark-text-secondary\",\n                                                                                children: \"Income\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 289,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiArrowRight_FiBarChart2_FiDollarSign_FiFileText_FiGithub_FiLinkedin_FiMail_FiPieChart_FiShield_FiTarget_FiTrendingUp_FiTwitter_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__.FiTrendingUp, {\n                                                                                className: \"text-success\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 290,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 288,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-xl font-bold text-light-text-primary dark:text-dark-text-primary\",\n                                                                        children: [\n                                                                            \"$\",\n                                                                            userData.income.toLocaleString()\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 292,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 287,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"p-4 rounded-xl bg-danger/10 dark:bg-danger/20 border border-danger/20 dark:border-danger/30 finance-card\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex justify-between items-center mb-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm text-light-text-secondary dark:text-dark-text-secondary\",\n                                                                                children: \"Expenses\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 299,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiArrowRight_FiBarChart2_FiDollarSign_FiFileText_FiGithub_FiLinkedin_FiMail_FiPieChart_FiShield_FiTarget_FiTrendingUp_FiTwitter_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__.FiBarChart2, {\n                                                                                className: \"text-danger\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 300,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 298,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-xl font-bold text-light-text-primary dark:text-dark-text-primary\",\n                                                                        children: [\n                                                                            \"$\",\n                                                                            userData.expenses.toLocaleString()\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 302,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 297,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 286,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-4 rounded-xl bg-primary/10 dark:bg-primary/20 border border-primary/20 dark:border-primary/30 finance-card\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between items-center mb-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm text-light-text-secondary dark:text-dark-text-secondary\",\n                                                                        children: \"Savings Goal\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 310,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiArrowRight_FiBarChart2_FiDollarSign_FiFileText_FiGithub_FiLinkedin_FiMail_FiPieChart_FiShield_FiTarget_FiTrendingUp_FiTwitter_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__.FiTarget, {\n                                                                        className: \"text-primary\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 311,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 309,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"relative pt-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex mb-2 items-center justify-between\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-xs font-semibold inline-block text-light-text-primary dark:text-dark-text-primary\",\n                                                                                    children: [\n                                                                                        userData.savingsGoal.percentage,\n                                                                                        \"% Complete\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 316,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 315,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-right\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-xs font-semibold inline-block text-light-text-muted dark:text-dark-text-muted\",\n                                                                                    children: [\n                                                                                        \"$\",\n                                                                                        userData.savingsGoal.current.toLocaleString(),\n                                                                                        \" / $\",\n                                                                                        userData.savingsGoal.target.toLocaleString()\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 321,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 320,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 314,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"overflow-hidden h-2 mb-4 text-xs flex rounded-full bg-light-accent dark:bg-dark-bg\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                                                            className: \"shadow-none flex flex-col text-center whitespace-nowrap text-white justify-center bg-blue-gradient\",\n                                                                            initial: {\n                                                                                width: \"0%\"\n                                                                            },\n                                                                            animate: {\n                                                                                width: `${userData.savingsGoal.percentage}%`\n                                                                            },\n                                                                            transition: {\n                                                                                duration: 1.5,\n                                                                                ease: \"easeOut\"\n                                                                            }\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 327,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 326,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 313,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 308,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 265,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 253,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 248,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 186,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 185,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 184,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                id: \"features\",\n                className: \"relative py-20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto max-w-7xl px-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                            className: \"text-center mb-16\",\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            viewport: {\n                                once: true,\n                                margin: \"-100px\"\n                            },\n                            transition: {\n                                duration: 0.5\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"inline-block px-4 py-2 rounded-full text-sm font-medium bg-primary/10 dark:bg-primary/20 text-primary dark:text-primary mb-4\",\n                                    children: \"Features\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 353,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"font-orbitron text-4xl md:text-5xl font-bold mb-4 letter-spacing-wide\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-glow dark:text-glow text-primary\",\n                                        children: \"NEXT-GEN FINANCIAL TOOLS\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 357,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 356,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xl font-rajdhani max-w-3xl mx-auto text-light-text-secondary dark:text-dark-text-secondary\",\n                                    children: [\n                                        \"Cutting-edge features engineered to revolutionize how you \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-semibold text-primary\",\n                                            children: \"analyze\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 362,\n                                            columnNumber: 73\n                                        }, this),\n                                        \",\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-semibold text-secondary\",\n                                            children: \" optimize\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 363,\n                                            columnNumber: 15\n                                        }, this),\n                                        \", and\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-semibold text-primary\",\n                                            children: \" maximize\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 364,\n                                            columnNumber: 15\n                                        }, this),\n                                        \" your financial potential\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 361,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 346,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                            children: features.map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                    className: \"relative group\",\n                                    initial: {\n                                        opacity: 0,\n                                        y: 20\n                                    },\n                                    whileInView: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    viewport: {\n                                        once: true,\n                                        margin: \"-100px\"\n                                    },\n                                    transition: {\n                                        duration: 0.5,\n                                        delay: index * 0.1\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 bg-gradient-to-r from-primary/10 to-secondary/10 rounded-xl blur-xl opacity-0 group-hover:opacity-100 transition-opacity\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 378,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                            className: \"relative p-6 rounded-xl border border-light-border dark:border-dark-border bg-light-card dark:bg-dark-card finance-card h-full\",\n                                            whileHover: {\n                                                y: -5,\n                                                boxShadow: \"var(--glow-primary)\"\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-primary mb-4 text-3xl\",\n                                                    children: feature.icon\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 386,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-xl font-semibold mb-2 text-light-text-primary dark:text-dark-text-primary\",\n                                                    children: feature.title\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 387,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-light-text-secondary dark:text-dark-text-secondary\",\n                                                    children: feature.description\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 388,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 379,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 370,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 368,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 345,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 344,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"relative py-20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto max-w-7xl px-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                        className: \"rounded-2xl glass-card p-8 md:p-12\",\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        whileInView: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        viewport: {\n                            once: true,\n                            margin: \"-100px\"\n                        },\n                        transition: {\n                            duration: 0.5\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\",\n                            children: [\n                                {\n                                    label: \"Active Users\",\n                                    value: 25000,\n                                    suffix: \"+\"\n                                },\n                                {\n                                    label: \"Transactions Processed\",\n                                    value: 1500000,\n                                    suffix: \"+\"\n                                },\n                                {\n                                    label: \"Savings Goals Achieved\",\n                                    value: 8700,\n                                    suffix: \"+\"\n                                },\n                                {\n                                    label: \"Customer Satisfaction\",\n                                    value: 98,\n                                    suffix: \"%\"\n                                }\n                            ].map((stat, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                    className: \"text-center\",\n                                    initial: {\n                                        opacity: 0,\n                                        y: 20\n                                    },\n                                    whileInView: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    viewport: {\n                                        once: true,\n                                        margin: \"-100px\"\n                                    },\n                                    transition: {\n                                        duration: 0.5,\n                                        delay: index * 0.1\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-4xl font-bold mb-2 text-light-text-primary dark:text-dark-text-primary\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AnimatedCounter, {\n                                                    value: stat.value,\n                                                    duration: 0.8\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 422,\n                                                    columnNumber: 21\n                                                }, this),\n                                                stat.suffix\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 421,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-light-text-secondary dark:text-dark-text-secondary\",\n                                            children: stat.label\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 424,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 413,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 406,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 399,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 398,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 397,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"relative py-20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto max-w-7xl px-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                        className: \"text-center\",\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        whileInView: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        viewport: {\n                            once: true,\n                            margin: \"-100px\"\n                        },\n                        transition: {\n                            duration: 0.5\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"block text-3xl md:text-4xl font-rajdhani font-bold text-light-text-primary dark:text-dark-text-primary mb-2\",\n                                        children: \"READY TO ELEVATE YOUR\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 443,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"block text-5xl md:text-6xl font-audiowide text-glow-green text-secondary letter-spacing-wide\",\n                                        children: \"FINANCIAL INTELLIGENCE?\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 446,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 442,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl font-rajdhani max-w-3xl mx-auto mb-8 text-light-text-secondary dark:text-dark-text-secondary\",\n                                children: [\n                                    \"Join the \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-semibold text-primary\",\n                                        children: \"10,000+\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 451,\n                                        columnNumber: 24\n                                    }, this),\n                                    \" users who are already leveraging our platform to\",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-semibold text-secondary\",\n                                        children: \" build wealth\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 452,\n                                        columnNumber: 15\n                                    }, this),\n                                    \",\",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-semibold text-primary\",\n                                        children: \" reduce debt\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 453,\n                                        columnNumber: 15\n                                    }, this),\n                                    \", and\",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-semibold text-secondary\",\n                                        children: \" secure\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 454,\n                                        columnNumber: 15\n                                    }, this),\n                                    \" their financial future\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 450,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: isLoggedIn ? \"/dashboard\" : \"/auth\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.button, {\n                                    className: \"group flex items-center justify-center px-8 py-4 text-lg font-medium rounded-xl text-dark-bg bg-secondary hover:bg-secondary-400 transition-all duration-300 mx-auto shadow-md\",\n                                    whileHover: {\n                                        scale: 1.05,\n                                        boxShadow: \"var(--glow-secondary)\"\n                                    },\n                                    whileTap: {\n                                        scale: 0.98\n                                    },\n                                    children: [\n                                        isLoggedIn ? \"Go to Dashboard\" : \"Get Started Now\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiArrowRight_FiBarChart2_FiDollarSign_FiFileText_FiGithub_FiLinkedin_FiMail_FiPieChart_FiShield_FiTarget_FiTrendingUp_FiTwitter_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__.FiArrowRight, {\n                                            className: \"ml-2 group-hover:translate-x-1 transition-transform\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 466,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 457,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 456,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 435,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 434,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 433,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                className: \"relative py-12 border-t border-light-border dark:border-dark-border bg-light-accent dark:bg-dark-accent\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto max-w-7xl px-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-3 gap-8 mb-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col items-center md:items-start\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: \"/\",\n                                            className: \"flex items-center mb-4 hover:opacity-80 transition-opacity\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-10 h-10 rounded-lg bg-gradient-to-br from-primary to-secondary flex items-center justify-center mr-2 shadow-md\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-dark-bg font-bold text-xl\",\n                                                        children: \"C\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 481,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 480,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-audiowide text-xl text-light-text-primary dark:text-dark-text-primary\",\n                                                    children: \"Cashminder\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 483,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 479,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-light-text-secondary dark:text-dark-text-secondary text-center md:text-left mb-4\",\n                                            children: \"Your intelligent financial companion for smarter money management and wealth building.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 487,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex space-x-4 mt-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: \"https://twitter.com/I_m_shivansh\",\n                                                    target: \"_blank\",\n                                                    rel: \"noopener noreferrer\",\n                                                    \"aria-label\": \"Twitter\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-8 h-8 rounded-full bg-primary/10 dark:bg-primary/20 flex items-center justify-center hover:bg-primary/20 dark:hover:bg-primary/30 transition-colors cursor-pointer\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiArrowRight_FiBarChart2_FiDollarSign_FiFileText_FiGithub_FiLinkedin_FiMail_FiPieChart_FiShield_FiTarget_FiTrendingUp_FiTwitter_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__.FiTwitter, {\n                                                            className: \"text-primary\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 493,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 492,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 491,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: \"https://github.com/shivanshpathak01\",\n                                                    target: \"_blank\",\n                                                    rel: \"noopener noreferrer\",\n                                                    \"aria-label\": \"GitHub\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-8 h-8 rounded-full bg-primary/10 dark:bg-primary/20 flex items-center justify-center hover:bg-primary/20 dark:hover:bg-primary/30 transition-colors cursor-pointer\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiArrowRight_FiBarChart2_FiDollarSign_FiFileText_FiGithub_FiLinkedin_FiMail_FiPieChart_FiShield_FiTarget_FiTrendingUp_FiTwitter_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__.FiGithub, {\n                                                            className: \"text-primary\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 498,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 497,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 496,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: \"https://www.linkedin.com/in/shivansh-pathak-02a72121a/\",\n                                                    target: \"_blank\",\n                                                    rel: \"noopener noreferrer\",\n                                                    \"aria-label\": \"LinkedIn\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-8 h-8 rounded-full bg-primary/10 dark:bg-primary/20 flex items-center justify-center hover:bg-primary/20 dark:hover:bg-primary/30 transition-colors cursor-pointer\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiArrowRight_FiBarChart2_FiDollarSign_FiFileText_FiGithub_FiLinkedin_FiMail_FiPieChart_FiShield_FiTarget_FiTrendingUp_FiTwitter_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__.FiLinkedin, {\n                                                            className: \"text-primary\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 503,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 502,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 501,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 490,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 478,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col items-center md:items-start\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-rajdhani font-bold text-xl mb-4 text-light-text-primary dark:text-dark-text-primary\",\n                                            children: \"Quick Links\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 511,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                        href: \"/dashboard\",\n                                                        className: \"text-light-text-secondary dark:text-dark-text-secondary hover:text-primary dark:hover:text-primary transition-colors flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiArrowRight_FiBarChart2_FiDollarSign_FiFileText_FiGithub_FiLinkedin_FiMail_FiPieChart_FiShield_FiTarget_FiTrendingUp_FiTwitter_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__.FiArrowRight, {\n                                                                className: \"mr-2 text-primary\",\n                                                                size: 14\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 517,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"Dashboard\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 516,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 515,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                        href: \"/transactions\",\n                                                        className: \"text-light-text-secondary dark:text-dark-text-secondary hover:text-primary dark:hover:text-primary transition-colors flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiArrowRight_FiBarChart2_FiDollarSign_FiFileText_FiGithub_FiLinkedin_FiMail_FiPieChart_FiShield_FiTarget_FiTrendingUp_FiTwitter_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__.FiArrowRight, {\n                                                                className: \"mr-2 text-primary\",\n                                                                size: 14\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 523,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"Transactions\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 522,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 521,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                        href: \"/analytics\",\n                                                        className: \"text-light-text-secondary dark:text-dark-text-secondary hover:text-primary dark:hover:text-primary transition-colors flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiArrowRight_FiBarChart2_FiDollarSign_FiFileText_FiGithub_FiLinkedin_FiMail_FiPieChart_FiShield_FiTarget_FiTrendingUp_FiTwitter_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__.FiArrowRight, {\n                                                                className: \"mr-2 text-primary\",\n                                                                size: 14\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 529,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"Analytics\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 528,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 527,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                        href: \"/goals\",\n                                                        className: \"text-light-text-secondary dark:text-dark-text-secondary hover:text-primary dark:hover:text-primary transition-colors flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiArrowRight_FiBarChart2_FiDollarSign_FiFileText_FiGithub_FiLinkedin_FiMail_FiPieChart_FiShield_FiTarget_FiTrendingUp_FiTwitter_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__.FiArrowRight, {\n                                                                className: \"mr-2 text-primary\",\n                                                                size: 14\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 535,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"Goals\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 534,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 533,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 514,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 510,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col items-center md:items-start\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-rajdhani font-bold text-xl mb-4 text-light-text-primary dark:text-dark-text-primary\",\n                                            children: \"Legal & Contact\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 544,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                        href: \"/privacy\",\n                                                        className: \"text-light-text-secondary dark:text-dark-text-secondary hover:text-primary dark:hover:text-primary transition-colors flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiArrowRight_FiBarChart2_FiDollarSign_FiFileText_FiGithub_FiLinkedin_FiMail_FiPieChart_FiShield_FiTarget_FiTrendingUp_FiTwitter_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__.FiShield, {\n                                                                className: \"mr-2 text-primary\",\n                                                                size: 14\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 550,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"Privacy Policy\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 549,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 548,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                        href: \"/terms\",\n                                                        className: \"text-light-text-secondary dark:text-dark-text-secondary hover:text-primary dark:hover:text-primary transition-colors flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiArrowRight_FiBarChart2_FiDollarSign_FiFileText_FiGithub_FiLinkedin_FiMail_FiPieChart_FiShield_FiTarget_FiTrendingUp_FiTwitter_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__.FiFileText, {\n                                                                className: \"mr-2 text-primary\",\n                                                                size: 14\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 556,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"Terms of Service\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 555,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 554,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                        href: \"/contact\",\n                                                        className: \"text-light-text-secondary dark:text-dark-text-secondary hover:text-primary dark:hover:text-primary transition-colors flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiArrowRight_FiBarChart2_FiDollarSign_FiFileText_FiGithub_FiLinkedin_FiMail_FiPieChart_FiShield_FiTarget_FiTrendingUp_FiTwitter_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__.FiMail, {\n                                                                className: \"mr-2 text-primary\",\n                                                                size: 14\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 562,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"Contact the Developer\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 561,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 560,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 547,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 543,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 476,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"pt-8 border-t border-light-border dark:border-dark-border text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-light-text-secondary dark:text-dark-text-secondary mb-2\",\n                                    children: [\n                                        \"Made with \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-red-500\",\n                                            children: \"❤️\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 572,\n                                            columnNumber: 25\n                                        }, this),\n                                        \" by Shivansh Pathak\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 571,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-light-text-muted dark:text-dark-text-muted\",\n                                    children: [\n                                        \"\\xa9 \",\n                                        new Date().getFullYear(),\n                                        \" Cashminder. All rights reserved.\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 574,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 570,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 475,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 474,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 131,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ThemeWrapper.tsx":
/*!*****************************************!*\
  !*** ./src/components/ThemeWrapper.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ThemeWrapper)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _context_ThemeContext__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/context/ThemeContext */ \"(ssr)/./src/context/ThemeContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction ThemeWrapper({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_context_ThemeContext__WEBPACK_IMPORTED_MODULE_1__.ThemeProvider, {\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\components\\\\ThemeWrapper.tsx\",\n        lineNumber: 7,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9UaGVtZVdyYXBwZXIudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBRXVEO0FBRXhDLFNBQVNDLGFBQWEsRUFBRUMsUUFBUSxFQUFpQztJQUM5RSxxQkFDRSw4REFBQ0YsZ0VBQWFBO2tCQUNYRTs7Ozs7O0FBR1AiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcc2hpdmFcXE9uZURyaXZlXFxEZXNrdG9wXFxta1xcQ2FzaG1pbmRlci0tLU1vbmV5LU1hbmFnZW1lbnQtQXBwXFxzcmNcXGNvbXBvbmVudHNcXFRoZW1lV3JhcHBlci50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xuXG5pbXBvcnQgeyBUaGVtZVByb3ZpZGVyIH0gZnJvbSAnQC9jb250ZXh0L1RoZW1lQ29udGV4dCc7XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFRoZW1lV3JhcHBlcih7IGNoaWxkcmVuIH06IHsgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZSB9KSB7XG4gIHJldHVybiAoXG4gICAgPFRoZW1lUHJvdmlkZXI+XG4gICAgICB7Y2hpbGRyZW59XG4gICAgPC9UaGVtZVByb3ZpZGVyPlxuICApO1xufVxuIl0sIm5hbWVzIjpbIlRoZW1lUHJvdmlkZXIiLCJUaGVtZVdyYXBwZXIiLCJjaGlsZHJlbiJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ThemeWrapper.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/FuturisticNavbar.tsx":
/*!****************************************************!*\
  !*** ./src/components/layout/FuturisticNavbar.tsx ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ FuturisticNavbar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var _ui_FuturisticThemeToggle__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../ui/FuturisticThemeToggle */ \"(ssr)/./src/components/ui/FuturisticThemeToggle.tsx\");\n/* harmony import */ var _context_ThemeContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/context/ThemeContext */ \"(ssr)/./src/context/ThemeContext.tsx\");\n/* harmony import */ var _barrel_optimize_names_FiDollarSign_FiHome_FiLogOut_FiMenu_FiPieChart_FiSettings_FiTarget_FiUser_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=FiDollarSign,FiHome,FiLogOut,FiMenu,FiPieChart,FiSettings,FiTarget,FiUser,FiX!=!react-icons/fi */ \"(ssr)/./node_modules/react-icons/fi/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\nfunction FuturisticNavbar() {\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isScrolled, setIsScrolled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    const { theme } = (0,_context_ThemeContext__WEBPACK_IMPORTED_MODULE_5__.useTheme)();\n    const isDark = theme === 'dark';\n    // Check if user is logged in\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FuturisticNavbar.useEffect\": ()=>{\n            // Function to check user authentication status\n            const checkAuth = {\n                \"FuturisticNavbar.useEffect.checkAuth\": ()=>{\n                    const userData = localStorage.getItem('cashminder_user');\n                    if (userData) {\n                        setUser(JSON.parse(userData));\n                    } else {\n                        setUser(null);\n                    }\n                }\n            }[\"FuturisticNavbar.useEffect.checkAuth\"];\n            // Check on initial load\n            checkAuth();\n            // Set up event listener for storage changes (for when user logs in/out in another tab)\n            window.addEventListener('storage', checkAuth);\n            // Set up custom event listener for auth changes within the same tab\n            window.addEventListener('auth_state_changed', checkAuth);\n            // Check auth status every 5 seconds to ensure UI is in sync\n            const interval = setInterval(checkAuth, 5000);\n            return ({\n                \"FuturisticNavbar.useEffect\": ()=>{\n                    window.removeEventListener('storage', checkAuth);\n                    window.removeEventListener('auth_state_changed', checkAuth);\n                    clearInterval(interval);\n                }\n            })[\"FuturisticNavbar.useEffect\"];\n        }\n    }[\"FuturisticNavbar.useEffect\"], []);\n    // Handle scroll effect\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FuturisticNavbar.useEffect\": ()=>{\n            const handleScroll = {\n                \"FuturisticNavbar.useEffect.handleScroll\": ()=>{\n                    setIsScrolled(window.scrollY > 10);\n                }\n            }[\"FuturisticNavbar.useEffect.handleScroll\"];\n            window.addEventListener('scroll', handleScroll);\n            return ({\n                \"FuturisticNavbar.useEffect\": ()=>window.removeEventListener('scroll', handleScroll)\n            })[\"FuturisticNavbar.useEffect\"];\n        }\n    }[\"FuturisticNavbar.useEffect\"], []);\n    // Navigation items based on authentication status\n    const authenticatedNavItems = [\n        {\n            name: 'Dashboard',\n            href: '/dashboard',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiDollarSign_FiHome_FiLogOut_FiMenu_FiPieChart_FiSettings_FiTarget_FiUser_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__.FiHome, {\n                className: \"w-5 h-5\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\components\\\\layout\\\\FuturisticNavbar.tsx\",\n                lineNumber: 71,\n                columnNumber: 52\n            }, this)\n        },\n        {\n            name: 'Transactions',\n            href: '/transactions',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiDollarSign_FiHome_FiLogOut_FiMenu_FiPieChart_FiSettings_FiTarget_FiUser_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__.FiDollarSign, {\n                className: \"w-5 h-5\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\components\\\\layout\\\\FuturisticNavbar.tsx\",\n                lineNumber: 72,\n                columnNumber: 58\n            }, this)\n        },\n        {\n            name: 'Analytics',\n            href: '/analytics',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiDollarSign_FiHome_FiLogOut_FiMenu_FiPieChart_FiSettings_FiTarget_FiUser_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__.FiPieChart, {\n                className: \"w-5 h-5\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\components\\\\layout\\\\FuturisticNavbar.tsx\",\n                lineNumber: 73,\n                columnNumber: 52\n            }, this)\n        },\n        {\n            name: 'Goals',\n            href: '/goals',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiDollarSign_FiHome_FiLogOut_FiMenu_FiPieChart_FiSettings_FiTarget_FiUser_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__.FiTarget, {\n                className: \"w-5 h-5\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\components\\\\layout\\\\FuturisticNavbar.tsx\",\n                lineNumber: 74,\n                columnNumber: 44\n            }, this)\n        },\n        {\n            name: 'Settings',\n            href: '/settings',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiDollarSign_FiHome_FiLogOut_FiMenu_FiPieChart_FiSettings_FiTarget_FiUser_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__.FiSettings, {\n                className: \"w-5 h-5\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\components\\\\layout\\\\FuturisticNavbar.tsx\",\n                lineNumber: 75,\n                columnNumber: 50\n            }, this)\n        }\n    ];\n    const unauthenticatedNavItems = [\n        {\n            name: 'Home',\n            href: '/',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiDollarSign_FiHome_FiLogOut_FiMenu_FiPieChart_FiSettings_FiTarget_FiUser_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__.FiHome, {\n                className: \"w-5 h-5\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\components\\\\layout\\\\FuturisticNavbar.tsx\",\n                lineNumber: 79,\n                columnNumber: 38\n            }, this)\n        },\n        {\n            name: 'Features',\n            href: '/#features',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiDollarSign_FiHome_FiLogOut_FiMenu_FiPieChart_FiSettings_FiTarget_FiUser_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__.FiPieChart, {\n                className: \"w-5 h-5\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\components\\\\layout\\\\FuturisticNavbar.tsx\",\n                lineNumber: 80,\n                columnNumber: 51\n            }, this)\n        },\n        {\n            name: 'Pricing',\n            href: '/pricing',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiDollarSign_FiHome_FiLogOut_FiMenu_FiPieChart_FiSettings_FiTarget_FiUser_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__.FiDollarSign, {\n                className: \"w-5 h-5\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\components\\\\layout\\\\FuturisticNavbar.tsx\",\n                lineNumber: 81,\n                columnNumber: 48\n            }, this)\n        }\n    ];\n    // Use the appropriate navigation items based on authentication status\n    const navItems = user ? authenticatedNavItems : unauthenticatedNavItems;\n    const handleLogout = ()=>{\n        // Remove user data from localStorage\n        localStorage.removeItem('cashminder_user');\n        // Dispatch custom event to notify other components about auth state change\n        window.dispatchEvent(new Event('auth_state_changed'));\n        // Set user state to null\n        setUser(null);\n        // Redirect to home page\n        window.location.href = '/';\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.nav, {\n                className: `fixed top-0 left-0 right-0 z-50 ${isScrolled ? 'bg-light-bg/95 dark:bg-dark-bg/95 backdrop-blur-md border-b border-light-border dark:border-dark-border shadow-sm' : 'bg-transparent'} transition-all duration-300`,\n                initial: {\n                    y: -100\n                },\n                animate: {\n                    y: 0\n                },\n                transition: {\n                    type: 'spring',\n                    stiffness: 300,\n                    damping: 30\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-between h-16\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: \"/\",\n                                            className: \"flex-shrink-0 flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                                    className: \"w-10 h-10 rounded-lg bg-gradient-to-br from-primary to-secondary flex items-center justify-center mr-2 shadow-md\",\n                                                    whileHover: {\n                                                        scale: 1.05,\n                                                        rotate: 5\n                                                    },\n                                                    whileTap: {\n                                                        scale: 0.95\n                                                    },\n                                                    animate: {\n                                                        boxShadow: [\n                                                            '0 0 0 rgba(0, 198, 255, 0.4)',\n                                                            '0 0 15px rgba(0, 198, 255, 0.6)',\n                                                            '0 0 0 rgba(0, 198, 255, 0.4)'\n                                                        ]\n                                                    },\n                                                    transition: {\n                                                        duration: 2,\n                                                        repeat: Infinity,\n                                                        repeatType: \"reverse\"\n                                                    },\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.span, {\n                                                        className: \"text-dark-bg font-bold text-xl\",\n                                                        animate: {\n                                                            scale: [\n                                                                1,\n                                                                1.1,\n                                                                1\n                                                            ]\n                                                        },\n                                                        transition: {\n                                                            duration: 2,\n                                                            repeat: Infinity\n                                                        },\n                                                        children: \"C\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\components\\\\layout\\\\FuturisticNavbar.tsx\",\n                                                        lineNumber: 132,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\components\\\\layout\\\\FuturisticNavbar.tsx\",\n                                                    lineNumber: 119,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-bold text-xl text-light-text-primary dark:text-dark-text-primary\",\n                                                    children: \"Cashminder\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\components\\\\layout\\\\FuturisticNavbar.tsx\",\n                                                    lineNumber: 142,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\components\\\\layout\\\\FuturisticNavbar.tsx\",\n                                            lineNumber: 118,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"hidden md:ml-6 md:flex md:space-x-4\",\n                                            children: navItems.map((item)=>{\n                                                const isActive = pathname === item.href;\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: item.href,\n                                                    className: `relative px-3 py-2 rounded-md text-sm font-medium flex items-center space-x-1 transition-all duration-200 ${isActive ? 'text-primary bg-primary/10 dark:bg-primary/20 border border-primary/20 dark:border-primary/30' : 'text-light-text-secondary dark:text-dark-text-secondary hover:text-primary dark:hover:text-primary hover:bg-light-accent dark:hover:bg-dark-accent'}`,\n                                                    children: [\n                                                        item.icon,\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: item.name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\components\\\\layout\\\\FuturisticNavbar.tsx\",\n                                                            lineNumber: 162,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        isActive && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                                            className: \"absolute bottom-0 left-0 right-0 h-0.5 bg-primary\",\n                                                            layoutId: \"navbar-indicator\",\n                                                            initial: {\n                                                                opacity: 0\n                                                            },\n                                                            animate: {\n                                                                opacity: 1\n                                                            },\n                                                            transition: {\n                                                                type: 'spring',\n                                                                stiffness: 300,\n                                                                damping: 30\n                                                            },\n                                                            style: {\n                                                                boxShadow: '0 0 8px rgba(0, 198, 255, 0.6)'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\components\\\\layout\\\\FuturisticNavbar.tsx\",\n                                                            lineNumber: 164,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, item.name, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\components\\\\layout\\\\FuturisticNavbar.tsx\",\n                                                    lineNumber: 152,\n                                                    columnNumber: 21\n                                                }, this);\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\components\\\\layout\\\\FuturisticNavbar.tsx\",\n                                            lineNumber: 148,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\components\\\\layout\\\\FuturisticNavbar.tsx\",\n                                    lineNumber: 116,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_FuturisticThemeToggle__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\components\\\\layout\\\\FuturisticNavbar.tsx\",\n                                            lineNumber: 184,\n                                            columnNumber: 15\n                                        }, this),\n                                        user ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative flex items-center space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"hidden md:block text-sm font-medium text-light-text-secondary dark:text-dark-text-secondary\",\n                                                    children: [\n                                                        \"Hello, \",\n                                                        user.name || 'User'\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\components\\\\layout\\\\FuturisticNavbar.tsx\",\n                                                    lineNumber: 190,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.button, {\n                                                    className: \"flex items-center space-x-2 px-3 py-2 rounded-full bg-light-accent dark:bg-dark-accent border border-light-border dark:border-dark-border transition-all duration-200\",\n                                                    whileHover: {\n                                                        scale: 1.05,\n                                                        boxShadow: \"var(--card-shadow)\"\n                                                    },\n                                                    whileTap: {\n                                                        scale: 0.95\n                                                    },\n                                                    onClick: handleLogout,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiDollarSign_FiHome_FiLogOut_FiMenu_FiPieChart_FiSettings_FiTarget_FiUser_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__.FiLogOut, {\n                                                            className: \"w-5 h-5 text-primary\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\components\\\\layout\\\\FuturisticNavbar.tsx\",\n                                                            lineNumber: 204,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm font-medium text-light-text-primary dark:text-dark-text-primary\",\n                                                            children: \"Logout\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\components\\\\layout\\\\FuturisticNavbar.tsx\",\n                                                            lineNumber: 205,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\components\\\\layout\\\\FuturisticNavbar.tsx\",\n                                                    lineNumber: 195,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\components\\\\layout\\\\FuturisticNavbar.tsx\",\n                                            lineNumber: 188,\n                                            columnNumber: 17\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: \"/auth\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.button, {\n                                                className: \"flex items-center space-x-2 px-4 py-2 rounded-full bg-primary hover:bg-primary-400 text-dark-bg transition-all duration-200 shadow-md\",\n                                                whileHover: {\n                                                    scale: 1.05,\n                                                    boxShadow: \"var(--glow-primary)\"\n                                                },\n                                                whileTap: {\n                                                    scale: 0.95\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiDollarSign_FiHome_FiLogOut_FiMenu_FiPieChart_FiSettings_FiTarget_FiUser_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__.FiUser, {\n                                                        className: \"w-5 h-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\components\\\\layout\\\\FuturisticNavbar.tsx\",\n                                                        lineNumber: 218,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-medium\",\n                                                        children: \"Login\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\components\\\\layout\\\\FuturisticNavbar.tsx\",\n                                                        lineNumber: 219,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\components\\\\layout\\\\FuturisticNavbar.tsx\",\n                                                lineNumber: 210,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\components\\\\layout\\\\FuturisticNavbar.tsx\",\n                                            lineNumber: 209,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex md:hidden\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.button, {\n                                                className: `inline-flex items-center justify-center p-2 rounded-md ${isDark ? 'text-dark-text-secondary hover:text-dark-text-primary hover:bg-dark-surface' : 'text-light-text-secondary hover:text-light-text-primary hover:bg-light-border'}`,\n                                                whileTap: {\n                                                    scale: 0.9\n                                                },\n                                                onClick: ()=>setIsOpen(!isOpen),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"sr-only\",\n                                                        children: \"Open main menu\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\components\\\\layout\\\\FuturisticNavbar.tsx\",\n                                                        lineNumber: 235,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    isOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiDollarSign_FiHome_FiLogOut_FiMenu_FiPieChart_FiSettings_FiTarget_FiUser_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__.FiX, {\n                                                        className: \"block h-6 w-6\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\components\\\\layout\\\\FuturisticNavbar.tsx\",\n                                                        lineNumber: 237,\n                                                        columnNumber: 21\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiDollarSign_FiHome_FiLogOut_FiMenu_FiPieChart_FiSettings_FiTarget_FiUser_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__.FiMenu, {\n                                                        className: \"block h-6 w-6\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\components\\\\layout\\\\FuturisticNavbar.tsx\",\n                                                        lineNumber: 239,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\components\\\\layout\\\\FuturisticNavbar.tsx\",\n                                                lineNumber: 226,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\components\\\\layout\\\\FuturisticNavbar.tsx\",\n                                            lineNumber: 225,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\components\\\\layout\\\\FuturisticNavbar.tsx\",\n                                    lineNumber: 182,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\components\\\\layout\\\\FuturisticNavbar.tsx\",\n                            lineNumber: 115,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\components\\\\layout\\\\FuturisticNavbar.tsx\",\n                        lineNumber: 114,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.AnimatePresence, {\n                        children: isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                            className: \"md:hidden bg-light-card dark:bg-dark-card border-t border-light-border dark:border-dark-border shadow-lg glass-card\",\n                            initial: {\n                                opacity: 0,\n                                height: 0\n                            },\n                            animate: {\n                                opacity: 1,\n                                height: 'auto'\n                            },\n                            exit: {\n                                opacity: 0,\n                                height: 0\n                            },\n                            transition: {\n                                duration: 0.3\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"px-2 pt-2 pb-3 space-y-1 sm:px-3\",\n                                children: navItems.map((item)=>{\n                                    const isActive = pathname === item.href;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: item.href,\n                                        className: `px-3 py-2 rounded-md text-base font-medium flex items-center space-x-3 ${isActive ? 'text-primary bg-primary/10 dark:bg-primary/20 border border-primary/20 dark:border-primary/30' : 'text-light-text-secondary dark:text-dark-text-secondary hover:text-primary dark:hover:text-primary hover:bg-light-accent dark:hover:bg-dark-accent'}`,\n                                        onClick: ()=>setIsOpen(false),\n                                        children: [\n                                            item.icon,\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: item.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\components\\\\layout\\\\FuturisticNavbar.tsx\",\n                                                lineNumber: 272,\n                                                columnNumber: 23\n                                            }, this)\n                                        ]\n                                    }, item.name, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\components\\\\layout\\\\FuturisticNavbar.tsx\",\n                                        lineNumber: 261,\n                                        columnNumber: 21\n                                    }, this);\n                                })\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\components\\\\layout\\\\FuturisticNavbar.tsx\",\n                                lineNumber: 257,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\components\\\\layout\\\\FuturisticNavbar.tsx\",\n                            lineNumber: 250,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\components\\\\layout\\\\FuturisticNavbar.tsx\",\n                        lineNumber: 248,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\components\\\\layout\\\\FuturisticNavbar.tsx\",\n                lineNumber: 104,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"h-16\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\components\\\\layout\\\\FuturisticNavbar.tsx\",\n                lineNumber: 283,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/FuturisticNavbar.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/FuturisticThemeToggle.tsx":
/*!*****************************************************!*\
  !*** ./src/components/ui/FuturisticThemeToggle.tsx ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ FuturisticThemeToggle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _context_ThemeContext__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/context/ThemeContext */ \"(ssr)/./src/context/ThemeContext.tsx\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction FuturisticThemeToggle() {\n    const { theme, toggleTheme } = (0,_context_ThemeContext__WEBPACK_IMPORTED_MODULE_1__.useTheme)();\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isAnimating, setIsAnimating] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    // Only show the toggle after mounting to avoid hydration mismatch\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"FuturisticThemeToggle.useEffect\": ()=>{\n            setMounted(true);\n        }\n    }[\"FuturisticThemeToggle.useEffect\"], []);\n    // Handle the toggle click\n    const handleToggle = ()=>{\n        if (isAnimating) return;\n        setIsAnimating(true);\n        toggleTheme();\n        // Reset animation state after animation completes\n        setTimeout(()=>{\n            setIsAnimating(false);\n        }, 600);\n    };\n    // Don't render anything until mounted to prevent hydration issues\n    if (!mounted) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-12 h-12\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\components\\\\ui\\\\FuturisticThemeToggle.tsx\",\n            lineNumber: 32,\n            columnNumber: 12\n        }, this);\n    }\n    const isDark = theme === 'dark';\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n        className: \"relative w-12 h-12 flex items-center justify-center\",\n        initial: false,\n        animate: {\n            scale: isAnimating ? 1.1 : 1\n        },\n        transition: {\n            duration: 0.3\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.button, {\n                onClick: handleToggle,\n                className: `w-12 h-12 rounded-full relative overflow-hidden flex items-center justify-center ${isDark ? 'bg-dark-card border border-dark-border shadow-inner' : 'bg-light-card border border-light-border shadow-md'} transition-all duration-300`,\n                whileHover: {\n                    scale: 1.05\n                },\n                whileTap: {\n                    scale: 0.95\n                },\n                \"aria-label\": `Switch to ${isDark ? 'light' : 'dark'} mode`,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.AnimatePresence, {\n                    mode: \"wait\",\n                    children: isDark ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                        initial: {\n                            y: 20,\n                            opacity: 0\n                        },\n                        animate: {\n                            y: 0,\n                            opacity: 1\n                        },\n                        exit: {\n                            y: -20,\n                            opacity: 0\n                        },\n                        transition: {\n                            duration: 0.3\n                        },\n                        className: \"absolute inset-0 flex items-center justify-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                    className: \"w-6 h-6 rounded-full bg-gray-100\",\n                                    animate: {\n                                        boxShadow: [\n                                            \"0 0 0px 0px rgba(255,255,255,0.5)\",\n                                            \"0 0 10px 2px rgba(255,255,255,0.3)\"\n                                        ]\n                                    },\n                                    transition: {\n                                        duration: 2,\n                                        repeat: Infinity,\n                                        repeatType: \"reverse\"\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\components\\\\ui\\\\FuturisticThemeToggle.tsx\",\n                                    lineNumber: 67,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                    className: \"absolute w-1 h-1 rounded-full bg-blue-200 top-0 right-0\",\n                                    animate: {\n                                        opacity: [\n                                            0.5,\n                                            1,\n                                            0.5\n                                        ]\n                                    },\n                                    transition: {\n                                        duration: 1.5,\n                                        repeat: Infinity\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\components\\\\ui\\\\FuturisticThemeToggle.tsx\",\n                                    lineNumber: 78,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                    className: \"absolute w-1 h-1 rounded-full bg-blue-200 bottom-1 left-0\",\n                                    animate: {\n                                        opacity: [\n                                            0.7,\n                                            0.3,\n                                            0.7\n                                        ]\n                                    },\n                                    transition: {\n                                        duration: 2,\n                                        repeat: Infinity\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\components\\\\ui\\\\FuturisticThemeToggle.tsx\",\n                                    lineNumber: 83,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\components\\\\ui\\\\FuturisticThemeToggle.tsx\",\n                            lineNumber: 66,\n                            columnNumber: 15\n                        }, this)\n                    }, \"dark-icon\", false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\components\\\\ui\\\\FuturisticThemeToggle.tsx\",\n                        lineNumber: 57,\n                        columnNumber: 13\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                        initial: {\n                            y: -20,\n                            opacity: 0\n                        },\n                        animate: {\n                            y: 0,\n                            opacity: 1\n                        },\n                        exit: {\n                            y: 20,\n                            opacity: 0\n                        },\n                        transition: {\n                            duration: 0.3\n                        },\n                        className: \"absolute inset-0 flex items-center justify-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                    className: \"w-6 h-6 rounded-full bg-yellow-400\",\n                                    animate: {\n                                        boxShadow: [\n                                            \"0 0 0px 0px rgba(250,204,21,0.5)\",\n                                            \"0 0 10px 2px rgba(250,204,21,0.3)\"\n                                        ]\n                                    },\n                                    transition: {\n                                        duration: 2,\n                                        repeat: Infinity,\n                                        repeatType: \"reverse\"\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\components\\\\ui\\\\FuturisticThemeToggle.tsx\",\n                                    lineNumber: 101,\n                                    columnNumber: 17\n                                }, this),\n                                [\n                                    ...Array(8)\n                                ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                        className: \"absolute w-1 h-2 bg-yellow-400\",\n                                        style: {\n                                            left: '50%',\n                                            top: '50%',\n                                            marginLeft: '-0.5px',\n                                            marginTop: '-1px',\n                                            transformOrigin: '50% 0',\n                                            transform: `rotate(${i * 45}deg) translateY(-5px)`\n                                        },\n                                        animate: {\n                                            height: [\n                                                2,\n                                                3,\n                                                2\n                                            ],\n                                            opacity: [\n                                                0.8,\n                                                1,\n                                                0.8\n                                            ]\n                                        },\n                                        transition: {\n                                            duration: 1.5,\n                                            repeat: Infinity,\n                                            delay: i * 0.1\n                                        }\n                                    }, i, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\components\\\\ui\\\\FuturisticThemeToggle.tsx\",\n                                        lineNumber: 114,\n                                        columnNumber: 19\n                                    }, this))\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\components\\\\ui\\\\FuturisticThemeToggle.tsx\",\n                            lineNumber: 100,\n                            columnNumber: 15\n                        }, this)\n                    }, \"light-icon\", false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\components\\\\ui\\\\FuturisticThemeToggle.tsx\",\n                        lineNumber: 91,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\components\\\\ui\\\\FuturisticThemeToggle.tsx\",\n                    lineNumber: 55,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\components\\\\ui\\\\FuturisticThemeToggle.tsx\",\n                lineNumber: 44,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.AnimatePresence, {\n                children: isAnimating && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                    initial: {\n                        scale: 0,\n                        opacity: 0.7\n                    },\n                    animate: {\n                        scale: 4,\n                        opacity: 0\n                    },\n                    exit: {\n                        opacity: 0\n                    },\n                    transition: {\n                        duration: 0.6\n                    },\n                    className: `absolute inset-0 rounded-full ${isDark ? 'bg-primary' : 'bg-primary'}`,\n                    style: {\n                        zIndex: -1\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\components\\\\ui\\\\FuturisticThemeToggle.tsx\",\n                    lineNumber: 145,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\components\\\\ui\\\\FuturisticThemeToggle.tsx\",\n                lineNumber: 143,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\components\\\\ui\\\\FuturisticThemeToggle.tsx\",\n        lineNumber: 38,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/FuturisticThemeToggle.tsx\n");

/***/ }),

/***/ "(ssr)/./src/context/ThemeContext.tsx":
/*!**************************************!*\
  !*** ./src/context/ThemeContext.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ThemeProvider: () => (/* binding */ ThemeProvider),\n/* harmony export */   useTheme: () => (/* binding */ useTheme)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ ThemeProvider,useTheme auto */ \n\nconst ThemeContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction ThemeProvider({ children }) {\n    const [theme, setTheme] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('light');\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ThemeProvider.useEffect\": ()=>{\n            // This effect runs on client-side only\n            try {\n                // Check local storage first\n                const savedTheme = localStorage.getItem('theme');\n                let themeToApply;\n                if (savedTheme && (savedTheme === 'dark' || savedTheme === 'light')) {\n                    themeToApply = savedTheme;\n                } else {\n                    // If no saved theme, check system preference\n                    const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;\n                    themeToApply = prefersDark ? 'dark' : 'light';\n                    // Save the initial theme preference\n                    localStorage.setItem('theme', themeToApply);\n                }\n                // Update state\n                setTheme(themeToApply);\n                // Apply theme to DOM\n                if (themeToApply === 'dark') {\n                    document.documentElement.classList.add('dark');\n                    document.body.classList.add('dark');\n                    console.log('Dark mode initialized');\n                } else {\n                    document.documentElement.classList.remove('dark');\n                    document.body.classList.remove('dark');\n                    console.log('Light mode initialized');\n                }\n                console.log('Theme initialized to:', themeToApply);\n                console.log('Dark class present:', document.documentElement.classList.contains('dark'));\n            } catch (error) {\n                console.error('Failed to initialize theme:', error);\n            }\n        }\n    }[\"ThemeProvider.useEffect\"], []);\n    const toggleTheme = ()=>{\n        try {\n            const newTheme = theme === 'light' ? 'dark' : 'light';\n            setTheme(newTheme);\n            // Force toggle the dark class on the html element\n            if (newTheme === 'dark') {\n                document.documentElement.classList.add('dark');\n                document.body.classList.add('dark');\n                console.log('Dark mode enabled');\n            } else {\n                document.documentElement.classList.remove('dark');\n                document.body.classList.remove('dark');\n                console.log('Light mode enabled');\n            }\n            // Save to localStorage\n            localStorage.setItem('theme', newTheme);\n            // Log the current state for debugging\n            console.log('Theme toggled to:', newTheme);\n            console.log('Dark class present:', document.documentElement.classList.contains('dark'));\n        } catch (error) {\n            console.error('Failed to toggle theme:', error);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ThemeContext.Provider, {\n        value: {\n            theme,\n            toggleTheme\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\context\\\\ThemeContext.tsx\",\n        lineNumber: 83,\n        columnNumber: 5\n    }, this);\n}\nfunction useTheme() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(ThemeContext);\n    if (context === undefined) {\n        throw new Error('useTheme must be used within a ThemeProvider');\n    }\n    return context;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29udGV4dC9UaGVtZUNvbnRleHQudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7QUFFOEU7QUFTOUUsTUFBTUssNkJBQWVKLG9EQUFhQSxDQUErQks7QUFFMUQsU0FBU0MsY0FBYyxFQUFFQyxRQUFRLEVBQWlDO0lBQ3ZFLE1BQU0sQ0FBQ0MsT0FBT0MsU0FBUyxHQUFHTiwrQ0FBUUEsQ0FBUTtJQUUxQ0QsZ0RBQVNBO21DQUFDO1lBQ1IsdUNBQXVDO1lBQ3ZDLElBQUk7Z0JBQ0YsNEJBQTRCO2dCQUM1QixNQUFNUSxhQUFhQyxhQUFhQyxPQUFPLENBQUM7Z0JBQ3hDLElBQUlDO2dCQUVKLElBQUlILGNBQWVBLENBQUFBLGVBQWUsVUFBVUEsZUFBZSxPQUFNLEdBQUk7b0JBQ25FRyxlQUFlSDtnQkFDakIsT0FBTztvQkFDTCw2Q0FBNkM7b0JBQzdDLE1BQU1JLGNBQWNDLE9BQU9DLFVBQVUsQ0FBQyxnQ0FBZ0NDLE9BQU87b0JBQzdFSixlQUFlQyxjQUFjLFNBQVM7b0JBQ3RDLG9DQUFvQztvQkFDcENILGFBQWFPLE9BQU8sQ0FBQyxTQUFTTDtnQkFDaEM7Z0JBRUEsZUFBZTtnQkFDZkosU0FBU0k7Z0JBRVQscUJBQXFCO2dCQUNyQixJQUFJQSxpQkFBaUIsUUFBUTtvQkFDM0JNLFNBQVNDLGVBQWUsQ0FBQ0MsU0FBUyxDQUFDQyxHQUFHLENBQUM7b0JBQ3ZDSCxTQUFTSSxJQUFJLENBQUNGLFNBQVMsQ0FBQ0MsR0FBRyxDQUFDO29CQUM1QkUsUUFBUUMsR0FBRyxDQUFDO2dCQUNkLE9BQU87b0JBQ0xOLFNBQVNDLGVBQWUsQ0FBQ0MsU0FBUyxDQUFDSyxNQUFNLENBQUM7b0JBQzFDUCxTQUFTSSxJQUFJLENBQUNGLFNBQVMsQ0FBQ0ssTUFBTSxDQUFDO29CQUMvQkYsUUFBUUMsR0FBRyxDQUFDO2dCQUNkO2dCQUVBRCxRQUFRQyxHQUFHLENBQUMseUJBQXlCWjtnQkFDckNXLFFBQVFDLEdBQUcsQ0FBQyx1QkFBdUJOLFNBQVNDLGVBQWUsQ0FBQ0MsU0FBUyxDQUFDTSxRQUFRLENBQUM7WUFDakYsRUFBRSxPQUFPQyxPQUFPO2dCQUNkSixRQUFRSSxLQUFLLENBQUMsK0JBQStCQTtZQUMvQztRQUNGO2tDQUFHLEVBQUU7SUFFTCxNQUFNQyxjQUFjO1FBQ2xCLElBQUk7WUFDRixNQUFNQyxXQUFXdEIsVUFBVSxVQUFVLFNBQVM7WUFDOUNDLFNBQVNxQjtZQUVULGtEQUFrRDtZQUNsRCxJQUFJQSxhQUFhLFFBQVE7Z0JBQ3ZCWCxTQUFTQyxlQUFlLENBQUNDLFNBQVMsQ0FBQ0MsR0FBRyxDQUFDO2dCQUN2Q0gsU0FBU0ksSUFBSSxDQUFDRixTQUFTLENBQUNDLEdBQUcsQ0FBQztnQkFDNUJFLFFBQVFDLEdBQUcsQ0FBQztZQUNkLE9BQU87Z0JBQ0xOLFNBQVNDLGVBQWUsQ0FBQ0MsU0FBUyxDQUFDSyxNQUFNLENBQUM7Z0JBQzFDUCxTQUFTSSxJQUFJLENBQUNGLFNBQVMsQ0FBQ0ssTUFBTSxDQUFDO2dCQUMvQkYsUUFBUUMsR0FBRyxDQUFDO1lBQ2Q7WUFFQSx1QkFBdUI7WUFDdkJkLGFBQWFPLE9BQU8sQ0FBQyxTQUFTWTtZQUU5QixzQ0FBc0M7WUFDdENOLFFBQVFDLEdBQUcsQ0FBQyxxQkFBcUJLO1lBQ2pDTixRQUFRQyxHQUFHLENBQUMsdUJBQXVCTixTQUFTQyxlQUFlLENBQUNDLFNBQVMsQ0FBQ00sUUFBUSxDQUFDO1FBQ2pGLEVBQUUsT0FBT0MsT0FBTztZQUNkSixRQUFRSSxLQUFLLENBQUMsMkJBQTJCQTtRQUMzQztJQUNGO0lBRUEscUJBQ0UsOERBQUN4QixhQUFhMkIsUUFBUTtRQUFDQyxPQUFPO1lBQUV4QjtZQUFPcUI7UUFBWTtrQkFDaER0Qjs7Ozs7O0FBR1A7QUFFTyxTQUFTMEI7SUFDZCxNQUFNQyxVQUFVakMsaURBQVVBLENBQUNHO0lBQzNCLElBQUk4QixZQUFZN0IsV0FBVztRQUN6QixNQUFNLElBQUk4QixNQUFNO0lBQ2xCO0lBQ0EsT0FBT0Q7QUFDVCIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxzaGl2YVxcT25lRHJpdmVcXERlc2t0b3BcXG1rXFxDYXNobWluZGVyLS0tTW9uZXktTWFuYWdlbWVudC1BcHBcXHNyY1xcY29udGV4dFxcVGhlbWVDb250ZXh0LnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XG5cbmltcG9ydCBSZWFjdCwgeyBjcmVhdGVDb250ZXh0LCB1c2VDb250ZXh0LCB1c2VFZmZlY3QsIHVzZVN0YXRlIH0gZnJvbSAncmVhY3QnO1xuXG50eXBlIFRoZW1lID0gJ2xpZ2h0JyB8ICdkYXJrJztcblxuaW50ZXJmYWNlIFRoZW1lQ29udGV4dFR5cGUge1xuICB0aGVtZTogVGhlbWU7XG4gIHRvZ2dsZVRoZW1lOiAoKSA9PiB2b2lkO1xufVxuXG5jb25zdCBUaGVtZUNvbnRleHQgPSBjcmVhdGVDb250ZXh0PFRoZW1lQ29udGV4dFR5cGUgfCB1bmRlZmluZWQ+KHVuZGVmaW5lZCk7XG5cbmV4cG9ydCBmdW5jdGlvbiBUaGVtZVByb3ZpZGVyKHsgY2hpbGRyZW4gfTogeyBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlIH0pIHtcbiAgY29uc3QgW3RoZW1lLCBzZXRUaGVtZV0gPSB1c2VTdGF0ZTxUaGVtZT4oJ2xpZ2h0Jyk7XG5cbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICAvLyBUaGlzIGVmZmVjdCBydW5zIG9uIGNsaWVudC1zaWRlIG9ubHlcbiAgICB0cnkge1xuICAgICAgLy8gQ2hlY2sgbG9jYWwgc3RvcmFnZSBmaXJzdFxuICAgICAgY29uc3Qgc2F2ZWRUaGVtZSA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKCd0aGVtZScpIGFzIFRoZW1lO1xuICAgICAgbGV0IHRoZW1lVG9BcHBseTogVGhlbWU7XG5cbiAgICAgIGlmIChzYXZlZFRoZW1lICYmIChzYXZlZFRoZW1lID09PSAnZGFyaycgfHwgc2F2ZWRUaGVtZSA9PT0gJ2xpZ2h0JykpIHtcbiAgICAgICAgdGhlbWVUb0FwcGx5ID0gc2F2ZWRUaGVtZTtcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIC8vIElmIG5vIHNhdmVkIHRoZW1lLCBjaGVjayBzeXN0ZW0gcHJlZmVyZW5jZVxuICAgICAgICBjb25zdCBwcmVmZXJzRGFyayA9IHdpbmRvdy5tYXRjaE1lZGlhKCcocHJlZmVycy1jb2xvci1zY2hlbWU6IGRhcmspJykubWF0Y2hlcztcbiAgICAgICAgdGhlbWVUb0FwcGx5ID0gcHJlZmVyc0RhcmsgPyAnZGFyaycgOiAnbGlnaHQnO1xuICAgICAgICAvLyBTYXZlIHRoZSBpbml0aWFsIHRoZW1lIHByZWZlcmVuY2VcbiAgICAgICAgbG9jYWxTdG9yYWdlLnNldEl0ZW0oJ3RoZW1lJywgdGhlbWVUb0FwcGx5KTtcbiAgICAgIH1cblxuICAgICAgLy8gVXBkYXRlIHN0YXRlXG4gICAgICBzZXRUaGVtZSh0aGVtZVRvQXBwbHkpO1xuXG4gICAgICAvLyBBcHBseSB0aGVtZSB0byBET01cbiAgICAgIGlmICh0aGVtZVRvQXBwbHkgPT09ICdkYXJrJykge1xuICAgICAgICBkb2N1bWVudC5kb2N1bWVudEVsZW1lbnQuY2xhc3NMaXN0LmFkZCgnZGFyaycpO1xuICAgICAgICBkb2N1bWVudC5ib2R5LmNsYXNzTGlzdC5hZGQoJ2RhcmsnKTtcbiAgICAgICAgY29uc29sZS5sb2coJ0RhcmsgbW9kZSBpbml0aWFsaXplZCcpO1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgZG9jdW1lbnQuZG9jdW1lbnRFbGVtZW50LmNsYXNzTGlzdC5yZW1vdmUoJ2RhcmsnKTtcbiAgICAgICAgZG9jdW1lbnQuYm9keS5jbGFzc0xpc3QucmVtb3ZlKCdkYXJrJyk7XG4gICAgICAgIGNvbnNvbGUubG9nKCdMaWdodCBtb2RlIGluaXRpYWxpemVkJyk7XG4gICAgICB9XG5cbiAgICAgIGNvbnNvbGUubG9nKCdUaGVtZSBpbml0aWFsaXplZCB0bzonLCB0aGVtZVRvQXBwbHkpO1xuICAgICAgY29uc29sZS5sb2coJ0RhcmsgY2xhc3MgcHJlc2VudDonLCBkb2N1bWVudC5kb2N1bWVudEVsZW1lbnQuY2xhc3NMaXN0LmNvbnRhaW5zKCdkYXJrJykpO1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdGYWlsZWQgdG8gaW5pdGlhbGl6ZSB0aGVtZTonLCBlcnJvcik7XG4gICAgfVxuICB9LCBbXSk7XG5cbiAgY29uc3QgdG9nZ2xlVGhlbWUgPSAoKSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IG5ld1RoZW1lID0gdGhlbWUgPT09ICdsaWdodCcgPyAnZGFyaycgOiAnbGlnaHQnO1xuICAgICAgc2V0VGhlbWUobmV3VGhlbWUpO1xuXG4gICAgICAvLyBGb3JjZSB0b2dnbGUgdGhlIGRhcmsgY2xhc3Mgb24gdGhlIGh0bWwgZWxlbWVudFxuICAgICAgaWYgKG5ld1RoZW1lID09PSAnZGFyaycpIHtcbiAgICAgICAgZG9jdW1lbnQuZG9jdW1lbnRFbGVtZW50LmNsYXNzTGlzdC5hZGQoJ2RhcmsnKTtcbiAgICAgICAgZG9jdW1lbnQuYm9keS5jbGFzc0xpc3QuYWRkKCdkYXJrJyk7XG4gICAgICAgIGNvbnNvbGUubG9nKCdEYXJrIG1vZGUgZW5hYmxlZCcpO1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgZG9jdW1lbnQuZG9jdW1lbnRFbGVtZW50LmNsYXNzTGlzdC5yZW1vdmUoJ2RhcmsnKTtcbiAgICAgICAgZG9jdW1lbnQuYm9keS5jbGFzc0xpc3QucmVtb3ZlKCdkYXJrJyk7XG4gICAgICAgIGNvbnNvbGUubG9nKCdMaWdodCBtb2RlIGVuYWJsZWQnKTtcbiAgICAgIH1cblxuICAgICAgLy8gU2F2ZSB0byBsb2NhbFN0b3JhZ2VcbiAgICAgIGxvY2FsU3RvcmFnZS5zZXRJdGVtKCd0aGVtZScsIG5ld1RoZW1lKTtcblxuICAgICAgLy8gTG9nIHRoZSBjdXJyZW50IHN0YXRlIGZvciBkZWJ1Z2dpbmdcbiAgICAgIGNvbnNvbGUubG9nKCdUaGVtZSB0b2dnbGVkIHRvOicsIG5ld1RoZW1lKTtcbiAgICAgIGNvbnNvbGUubG9nKCdEYXJrIGNsYXNzIHByZXNlbnQ6JywgZG9jdW1lbnQuZG9jdW1lbnRFbGVtZW50LmNsYXNzTGlzdC5jb250YWlucygnZGFyaycpKTtcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRmFpbGVkIHRvIHRvZ2dsZSB0aGVtZTonLCBlcnJvcik7XG4gICAgfVxuICB9O1xuXG4gIHJldHVybiAoXG4gICAgPFRoZW1lQ29udGV4dC5Qcm92aWRlciB2YWx1ZT17eyB0aGVtZSwgdG9nZ2xlVGhlbWUgfX0+XG4gICAgICB7Y2hpbGRyZW59XG4gICAgPC9UaGVtZUNvbnRleHQuUHJvdmlkZXI+XG4gICk7XG59XG5cbmV4cG9ydCBmdW5jdGlvbiB1c2VUaGVtZSgpIHtcbiAgY29uc3QgY29udGV4dCA9IHVzZUNvbnRleHQoVGhlbWVDb250ZXh0KTtcbiAgaWYgKGNvbnRleHQgPT09IHVuZGVmaW5lZCkge1xuICAgIHRocm93IG5ldyBFcnJvcigndXNlVGhlbWUgbXVzdCBiZSB1c2VkIHdpdGhpbiBhIFRoZW1lUHJvdmlkZXInKTtcbiAgfVxuICByZXR1cm4gY29udGV4dDtcbn1cbiJdLCJuYW1lcyI6WyJSZWFjdCIsImNyZWF0ZUNvbnRleHQiLCJ1c2VDb250ZXh0IiwidXNlRWZmZWN0IiwidXNlU3RhdGUiLCJUaGVtZUNvbnRleHQiLCJ1bmRlZmluZWQiLCJUaGVtZVByb3ZpZGVyIiwiY2hpbGRyZW4iLCJ0aGVtZSIsInNldFRoZW1lIiwic2F2ZWRUaGVtZSIsImxvY2FsU3RvcmFnZSIsImdldEl0ZW0iLCJ0aGVtZVRvQXBwbHkiLCJwcmVmZXJzRGFyayIsIndpbmRvdyIsIm1hdGNoTWVkaWEiLCJtYXRjaGVzIiwic2V0SXRlbSIsImRvY3VtZW50IiwiZG9jdW1lbnRFbGVtZW50IiwiY2xhc3NMaXN0IiwiYWRkIiwiYm9keSIsImNvbnNvbGUiLCJsb2ciLCJyZW1vdmUiLCJjb250YWlucyIsImVycm9yIiwidG9nZ2xlVGhlbWUiLCJuZXdUaGVtZSIsIlByb3ZpZGVyIiwidmFsdWUiLCJ1c2VUaGVtZSIsImNvbnRleHQiLCJFcnJvciJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/context/ThemeContext.tsx\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/framer-motion","vendor-chunks/react-icons","vendor-chunks/motion-dom","vendor-chunks/motion-utils","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5Cshiva%5COneDrive%5CDesktop%5Cmk%5CCashminder---Money-Management-App%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cshiva%5COneDrive%5CDesktop%5Cmk%5CCashminder---Money-Management-App&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();